import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';

export default function OnboardingLayout() {
  return (
    <>
      <StatusBar style="light" backgroundColor="#0F0F0F" />
      <Stack
        screenOptions={{
          headerShown: false,
          contentStyle: { backgroundColor: '#0F0F0F' },
          animation: 'slide_from_right',
        }}
      >
        <Stack.Screen 
          name="welcome" 
          options={{
            title: 'Welcome',
          }}
        />
        <Stack.Screen 
          name="features" 
          options={{
            title: 'Features',
          }}
        />
        <Stack.Screen 
          name="preferences" 
          options={{
            title: 'Preferences',
          }}
        />
        <Stack.Screen 
          name="complete" 
          options={{
            title: 'Setup Complete',
          }}
        />
      </Stack>
    </>
  );
}
