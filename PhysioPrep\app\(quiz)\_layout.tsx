import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';

export default function QuizLayout() {
  return (
    <>
      <StatusBar style="light" backgroundColor="#0F0F0F" />
      <Stack
        screenOptions={{
          headerShown: false,
          contentStyle: { backgroundColor: '#0F0F0F' },
          animation: 'slide_from_right',
        }}
      >
        <Stack.Screen 
          name="index" 
          options={{
            title: 'Quiz Selection',
          }}
        />
        <Stack.Screen 
          name="[subject]" 
          options={{
            title: 'Quiz Setup',
          }}
        />
        <Stack.Screen 
          name="active/[quizId]" 
          options={{
            title: 'Active Quiz',
            gestureEnabled: false, // Prevent back gesture during quiz
          }}
        />
        <Stack.Screen 
          name="results/[quizId]" 
          options={{
            title: 'Quiz Results',
          }}
        />
      </Stack>
    </>
  );
}
