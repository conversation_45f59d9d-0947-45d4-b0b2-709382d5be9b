{"name": "physioprep", "main": "index.js", "version": "1.0.0", "scripts": {"dev": "expo start -c", "dev:web": "expo start -c --web", "dev:android": "expo start -c --android", "android": "expo start -c --android", "ios": "expo start -c --ios", "web": "expo start -c --web", "clean": "rm -rf .expo node_modules", "postinstall": "npx tailwindcss -i ./global.css -o ./node_modules/.cache/nativewind/global.css"}, "dependencies": {"@react-navigation/native": "^7.0.0", "@rn-primitives/avatar": "~1.2.0", "@rn-primitives/portal": "~1.3.0", "@rn-primitives/progress": "~1.2.0", "@rn-primitives/slot": "~1.2.0", "@rn-primitives/tooltip": "~1.2.0", "@shopify/flash-list": "^1.6.3", "axios": "^1.6.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "date-fns": "^3.6.0", "expo": "^53.0.9", "expo-blur": "~13.0.2", "expo-linking": "~7.1.5", "expo-navigation-bar": "~4.2.4", "expo-router": "~5.0.7", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.7", "lucide-react-native": "^0.511.0", "moti": "^0.29.0", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-reanimated": "~3.17.5", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-web": "~0.20.0", "recoil": "^0.7.7", "tailwind-merge": "^2.2.1", "tailwindcss": "3.3.5", "tailwindcss-animate": "^1.0.7", "yup": "^1.4.0", "@react-native-async-storage/async-storage": "2.1.2"}, "devDependencies": {"@babel/core": "^7.26.0", "@types/react": "~19.0.14", "typescript": "^5.8.3"}, "private": true}