import * as React from 'react';
import { View, ScrollView, StatusBar, Alert } from 'react-native';
import { useRouter } from 'expo-router';
import { useRecoilValue } from 'recoil';
import { Mo<PERSON>View } from 'moti';
import {
  ArrowLeft,
  Settings,
  User,
  Mail,
  Bell,
  Shield,
  CreditCard,
  HelpCircle,
  LogOut,
  Edit3,
  Trophy,
  Target,
  Calendar,
  BarChart3,
  Crown,
  Moon,
  Sun,
  Smartphone,
  Globe,
  Lock,
  ChevronRight
} from 'lucide-react-native';
import { userState, isAuthenticatedState } from '~/lib/store/atoms';
import { useAuth } from '~/lib/hooks/useAuth';
import { Text } from '~/components/ui/text';
import { Button } from '~/components/ui/button';
import { AnimatedCard, StatsCard } from '~/components/ui/animated-card';
import { BottomTabs } from '~/components/navigation/bottom-tabs';
import { GridLayout } from '~/components/ui/grid-layout';
import { Collapsible } from '~/components/ui/collapsible';

export default function ProfileScreen() {
  const router = useRouter();
  const user = useRecoilValue(userState);
  const isAuthenticated = useRecoilValue(isAuthenticatedState);
  const { logout } = useAuth();

  React.useEffect(() => {
    if (!isAuthenticated) {
      router.replace('/auth');
    }
  }, [isAuthenticated]);

  const handleLogout = () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: async () => {
            await logout();
            router.replace('/');
          }
        }
      ]
    );
  };

  const handleEditProfile = () => {
    // Navigate to edit profile screen
    router.push('/edit-profile');
  };

  const handleUpgradeToPremium = () => {
    // Navigate to premium upgrade screen
    router.push('/premium');
  };

  if (!user) {
    return (
      <View className='flex-1 justify-center items-center bg-bg-100'>
        <Text className='text-text-100 text-lg'>Loading...</Text>
      </View>
    );
  }

  const getStreakEmoji = (streak: number) => {
    if (streak >= 30) return '🔥';
    if (streak >= 14) return '⚡';
    if (streak >= 7) return '💪';
    if (streak >= 3) return '🌟';
    return '👍';
  };

  return (
    <View className='flex-1 bg-bg-100'>
      <StatusBar barStyle="light-content" backgroundColor="#0F0F0F" />

      <ScrollView
        className='flex-1'
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 100 }}
      >
        {/* Header */}
        <MotiView
          from={{ opacity: 0, translateY: -30 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ type: 'spring', damping: 15 }}
          className='flex-row items-center justify-between px-6 pt-16 pb-6'
        >
          <Button
            onPress={() => router.back()}
            variant="ghost"
            size="sm"
            className='p-2'
          >
            <ArrowLeft size={24} color="#e0e0e0" />
          </Button>

          <Text className='text-text-100 text-xl font-bold'>Profile</Text>

          <Button
            onPress={handleEditProfile}
            variant="ghost"
            size="sm"
            className='p-2'
          >
            <Edit3 size={24} color="#e0e0e0" />
          </Button>
        </MotiView>

        {/* Profile Header */}
        <View className='px-6 mb-6'>
          <MotiView
            from={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ type: 'spring', damping: 15, delay: 200 }}
          >
            <AnimatedCard
              title=""
              variant="default"
              className='p-6'
            >
              <View className='items-center'>
                {/* Avatar */}
                <View className='w-20 h-20 bg-primary-100 rounded-full items-center justify-center mb-4'>
                  <Text className='text-text-100 text-2xl font-bold'>
                    {user.name.charAt(0).toUpperCase()}
                  </Text>
                </View>

                {/* User Info */}
                <Text className='text-text-100 text-xl font-bold mb-1'>
                  {user.name}
                </Text>
                <Text className='text-text-200 text-sm mb-2'>
                  {user.email}
                </Text>

                {/* Premium Badge */}
                {user.isPremiumActive ? (
                  <View className='flex-row items-center bg-accent-100/20 px-3 py-1 rounded-full mb-4'>
                    <Crown size={16} color="#00FFFF" />
                    <Text className='text-accent-100 text-sm font-semibold ml-1'>
                      Premium Member
                    </Text>
                  </View>
                ) : (
                  <Button
                    onPress={handleUpgradeToPremium}
                    className='bg-primary-100 px-6 py-2 mb-4'
                  >
                    <Crown size={16} color="#FFFFFF" />
                    <Text className='text-text-100 font-semibold ml-1'>
                      Upgrade to Premium
                    </Text>
                  </Button>
                )}

                {/* Email Verification Status */}
                {!user.isEmailVerified && (
                  <View className='flex-row items-center bg-yellow-500/20 px-3 py-1 rounded-full'>
                    <Mail size={16} color="#F59E0B" />
                    <Text className='text-yellow-400 text-sm ml-1'>
                      Email not verified
                    </Text>
                  </View>
                )}
              </View>
            </AnimatedCard>
          </MotiView>
        </View>

        {/* Stats Overview */}
        <View className='px-6 mb-6'>
          <Text className='text-text-100 text-lg font-semibold mb-4'>
            Your Statistics
          </Text>

          <GridLayout columns={2} spacing={12} animated={true}>
            <StatsCard
              title="Current Streak"
              value={`${user.stats.currentStreak} ${getStreakEmoji(user.stats.currentStreak)}`}
              subtitle="days in a row"
              icon={<Calendar size={20} color="#FF6B6B" />}
            />

            <StatsCard
              title="Accuracy"
              value={`${user.accuracyPercentage || 0}%`}
              subtitle={`${user.stats.correctAnswers}/${user.stats.totalQuestionsAnswered} correct`}
              icon={<Target size={20} color="#00FFFF" />}
            />

            <StatsCard
              title="Quizzes Taken"
              value={user.stats.totalQuizzesTaken.toString()}
              subtitle="practice sessions"
              icon={<BarChart3 size={20} color="#10B981" />}
            />

            <StatsCard
              title="Tests Completed"
              value={user.stats.totalTestsTaken.toString()}
              subtitle="full exams"
              icon={<Trophy size={20} color="#FFD700" />}
            />
          </GridLayout>
        </View>

        {/* Account Settings */}
        <View className='px-6 mb-6'>
          <Collapsible
            title="Account Settings"
            icon={<User size={20} color="#e0e0e0" />}
            defaultExpanded={true}
          >
            <View className='gap-3'>
              <Button
                onPress={handleEditProfile}
                variant="ghost"
                className='flex-row items-center justify-between p-3 bg-bg-300/30 rounded-lg'
              >
                <View className='flex-row items-center'>
                  <Edit3 size={20} color="#e0e0e0" />
                  <Text className='text-text-100 ml-3'>Edit Profile</Text>
                </View>
                <ChevronRight size={16} color="#e0e0e0" />
              </Button>

              <Button
                onPress={() => router.push('/change-password')}
                variant="ghost"
                className='flex-row items-center justify-between p-3 bg-bg-300/30 rounded-lg'
              >
                <View className='flex-row items-center'>
                  <Lock size={20} color="#e0e0e0" />
                  <Text className='text-text-100 ml-3'>Change Password</Text>
                </View>
                <ChevronRight size={16} color="#e0e0e0" />
              </Button>

              {!user.isEmailVerified && (
                <Button
                  onPress={() => {/* Resend verification */}}
                  variant="ghost"
                  className='flex-row items-center justify-between p-3 bg-yellow-500/10 rounded-lg'
                >
                  <View className='flex-row items-center'>
                    <Mail size={20} color="#F59E0B" />
                    <Text className='text-yellow-400 ml-3'>Verify Email</Text>
                  </View>
                  <ChevronRight size={16} color="#F59E0B" />
                </Button>
              )}
            </View>
          </Collapsible>
        </View>

        {/* App Settings */}
        <View className='px-6 mb-6'>
          <Collapsible
            title="App Settings"
            icon={<Settings size={20} color="#e0e0e0" />}
            defaultExpanded={false}
          >
            <View className='gap-3'>
              <Button
                onPress={() => router.push('/notifications-settings')}
                variant="ghost"
                className='flex-row items-center justify-between p-3 bg-bg-300/30 rounded-lg'
              >
                <View className='flex-row items-center'>
                  <Bell size={20} color="#e0e0e0" />
                  <Text className='text-text-100 ml-3'>Notifications</Text>
                </View>
                <ChevronRight size={16} color="#e0e0e0" />
              </Button>

              <Button
                onPress={() => {/* Theme settings */}}
                variant="ghost"
                className='flex-row items-center justify-between p-3 bg-bg-300/30 rounded-lg'
              >
                <View className='flex-row items-center'>
                  <Moon size={20} color="#e0e0e0" />
                  <Text className='text-text-100 ml-3'>Theme</Text>
                </View>
                <View className='flex-row items-center'>
                  <Text className='text-text-200 text-sm mr-2'>Dark</Text>
                  <ChevronRight size={16} color="#e0e0e0" />
                </View>
              </Button>

              <Button
                onPress={() => {/* Language settings */}}
                variant="ghost"
                className='flex-row items-center justify-between p-3 bg-bg-300/30 rounded-lg'
              >
                <View className='flex-row items-center'>
                  <Globe size={20} color="#e0e0e0" />
                  <Text className='text-text-100 ml-3'>Language</Text>
                </View>
                <View className='flex-row items-center'>
                  <Text className='text-text-200 text-sm mr-2'>English</Text>
                  <ChevronRight size={16} color="#e0e0e0" />
                </View>
              </Button>
            </View>
          </Collapsible>
        </View>

        {/* Premium Features */}
        {!user.isPremiumActive && (
          <View className='px-6 mb-6'>
            <MotiView
              from={{ opacity: 0, translateY: 20 }}
              animate={{ opacity: 1, translateY: 0 }}
              transition={{ type: 'spring', damping: 15, delay: 400 }}
            >
              <AnimatedCard
                title="Unlock Premium Features"
                description="Get access to advanced analytics, unlimited tests, and premium content"
                variant="accent"
                onPress={handleUpgradeToPremium}
                className='p-4'
              >
                <View className='flex-row items-center justify-between mt-3'>
                  <View className='flex-row items-center'>
                    <Crown size={20} color="#00FFFF" />
                    <Text className='text-accent-100 font-semibold ml-2'>
                      Upgrade Now
                    </Text>
                  </View>
                  <ChevronRight size={16} color="#00FFFF" />
                </View>
              </AnimatedCard>
            </MotiView>
          </View>
        )}

        {/* Support & Legal */}
        <View className='px-6 mb-6'>
          <Collapsible
            title="Support & Legal"
            icon={<HelpCircle size={20} color="#e0e0e0" />}
            defaultExpanded={false}
          >
            <View className='gap-3'>
              <Button
                onPress={() => router.push('/help')}
                variant="ghost"
                className='flex-row items-center justify-between p-3 bg-bg-300/30 rounded-lg'
              >
                <View className='flex-row items-center'>
                  <HelpCircle size={20} color="#e0e0e0" />
                  <Text className='text-text-100 ml-3'>Help & Support</Text>
                </View>
                <ChevronRight size={16} color="#e0e0e0" />
              </Button>

              <Button
                onPress={() => router.push('/privacy')}
                variant="ghost"
                className='flex-row items-center justify-between p-3 bg-bg-300/30 rounded-lg'
              >
                <View className='flex-row items-center'>
                  <Shield size={20} color="#e0e0e0" />
                  <Text className='text-text-100 ml-3'>Privacy Policy</Text>
                </View>
                <ChevronRight size={16} color="#e0e0e0" />
              </Button>

              <Button
                onPress={() => router.push('/terms')}
                variant="ghost"
                className='flex-row items-center justify-between p-3 bg-bg-300/30 rounded-lg'
              >
                <View className='flex-row items-center'>
                  <Globe size={20} color="#e0e0e0" />
                  <Text className='text-text-100 ml-3'>Terms of Service</Text>
                </View>
                <ChevronRight size={16} color="#e0e0e0" />
              </Button>
            </View>
          </Collapsible>
        </View>

        {/* Sign Out */}
        <View className='px-6 mb-6'>
          <MotiView
            from={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ type: 'spring', damping: 15, delay: 600 }}
          >
            <Button
              onPress={handleLogout}
              variant="outline"
              className='w-full py-4 border-red-500/30 bg-red-500/10'
            >
              <LogOut size={20} color="#EF4444" />
              <Text className='text-red-400 font-semibold text-lg ml-2'>
                Sign Out
              </Text>
            </Button>
          </MotiView>
        </View>

        {/* App Version */}
        <View className='px-6 mb-6'>
          <Text className='text-text-200 text-center text-sm'>
            PhysioPrep v1.0.0
          </Text>
          <Text className='text-text-200 text-center text-xs mt-1'>
            Made with ❤️ for physiotherapy students
          </Text>
        </View>
      </ScrollView>

      {/* Bottom Navigation */}
      <BottomTabs />
    </View>
  );
}
