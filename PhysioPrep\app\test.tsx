import * as React from 'react';
import { View, ScrollView, StatusBar, Pressable } from 'react-native';
import { useRouter } from 'expo-router';
import { <PERSON><PERSON>Vie<PERSON> } from 'moti';
import {
  ArrowLeft,
  Clock,
  FileText,
  Target,
  Filter,
  Play,
  Trophy,
  BarChart3,
  ChevronRight,
  Timer,
  CheckCircle,
  AlertCircle,
  Award,
  TrendingUp,
  Calendar,
  Users
} from 'lucide-react-native';
import { Text } from '~/components/ui/text';
import { But<PERSON> } from '~/components/ui/button';
import { AnimatedCard, StatsCard } from '~/components/ui/animated-card';
import { BottomTabs } from '~/components/navigation/bottom-tabs';
import { GridLayout, ResponsiveGrid } from '~/components/ui/grid-layout';
import { Collapsible } from '~/components/ui/collapsible';
import { StepperBreadcrumb } from '~/components/navigation/breadcrumb';

// Mock data for demonstration
const mockTestCategories = [
  {
    id: '1',
    name: 'Comprehensive Exam',
    description: 'Full-length practice exam covering all topics',
    duration: 180, // minutes
    questionCount: 150,
    passingScore: 70,
    difficulty: 'mixed',
    icon: '📋',
    color: '#FF6B6B',
    attempts: 3,
    bestScore: 85,
    averageScore: 78,
  },
  {
    id: '2',
    name: 'Anatomy Focus',
    description: 'Specialized test focusing on anatomical knowledge',
    duration: 90,
    questionCount: 75,
    passingScore: 75,
    difficulty: 'medium',
    icon: '🦴',
    color: '#00FFFF',
    attempts: 5,
    bestScore: 92,
    averageScore: 84,
  },
  {
    id: '3',
    name: 'Clinical Scenarios',
    description: 'Case-based questions for practical application',
    duration: 120,
    questionCount: 60,
    passingScore: 80,
    difficulty: 'hard',
    icon: '🏥',
    color: '#10B981',
    attempts: 2,
    bestScore: 76,
    averageScore: 72,
  },
  {
    id: '4',
    name: 'Quick Assessment',
    description: 'Short test for rapid knowledge check',
    duration: 30,
    questionCount: 25,
    passingScore: 70,
    difficulty: 'easy',
    icon: '⚡',
    color: '#F59E0B',
    attempts: 8,
    bestScore: 96,
    averageScore: 89,
  },
];

const mockRecentTests = [
  {
    id: '1',
    category: 'Comprehensive Exam',
    score: 85,
    passed: true,
    totalQuestions: 150,
    correctAnswers: 128,
    completedAt: '2 days ago',
    duration: '2h 45m',
    timeSpent: 165,
  },
  {
    id: '2',
    category: 'Anatomy Focus',
    score: 92,
    passed: true,
    totalQuestions: 75,
    correctAnswers: 69,
    completedAt: '1 week ago',
    duration: '1h 15m',
    timeSpent: 75,
  },
  {
    id: '3',
    category: 'Clinical Scenarios',
    score: 68,
    passed: false,
    totalQuestions: 60,
    correctAnswers: 41,
    completedAt: '2 weeks ago',
    duration: '1h 52m',
    timeSpent: 112,
  },
];

type TestMode = 'selection' | 'setup' | 'active' | 'results';

export default function TestScreen() {
  const router = useRouter();
  const [mode, setMode] = React.useState<TestMode>('selection');
  const [selectedTest, setSelectedTest] = React.useState<any>(null);
  const [testSettings, setTestSettings] = React.useState({
    showTimer: true,
    allowReview: true,
    autoSubmit: true,
    shuffleQuestions: false,
  });

  const handleTestSelect = (test: any) => {
    setSelectedTest(test);
    setMode('setup');
  };

  const handleStartTest = () => {
    setMode('active');
    // Here you would start the actual test
  };

  const handleBackToSelection = () => {
    setMode('selection');
    setSelectedTest(null);
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return '#10B981';
      case 'medium': return '#F59E0B';
      case 'hard': return '#EF4444';
      case 'mixed': return '#8B5CF6';
      default: return '#6B7280';
    }
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  const renderTestSelection = () => (
    <ScrollView
      className='flex-1'
      showsVerticalScrollIndicator={false}
      contentContainerStyle={{ paddingBottom: 100 }}
    >
      {/* Header */}
      <MotiView
        from={{ opacity: 0, translateY: -30 }}
        animate={{ opacity: 1, translateY: 0 }}
        transition={{ type: 'spring', damping: 15 }}
        className='flex-row items-center justify-between px-6 pt-16 pb-6'
      >
        <Button
          onPress={() => router.back()}
          variant="ghost"
          size="sm"
          className='p-2'
        >
          <ArrowLeft size={24} color="#e0e0e0" />
        </Button>

        <Text className='text-text-100 text-xl font-bold'>Practice Tests</Text>

        <Button
          onPress={() => {/* Filter functionality */}}
          variant="ghost"
          size="sm"
          className='p-2'
        >
          <Filter size={24} color="#e0e0e0" />
        </Button>
      </MotiView>

      {/* Quick Stats */}
      <View className='px-6 mb-6'>
        <GridLayout columns={3} spacing={12} animated={true}>
          <StatsCard
            title="Tests Taken"
            value="18"
            icon={<FileText size={16} color="#FF6B6B" />}
          />
          <StatsCard
            title="Pass Rate"
            value="89%"
            icon={<Target size={16} color="#10B981" />}
          />
          <StatsCard
            title="Avg Score"
            value="84%"
            icon={<Trophy size={16} color="#FFD700" />}
          />
        </GridLayout>
      </View>

      {/* Test Categories */}
      <View className='px-6 mb-6'>
        <Text className='text-text-100 text-lg font-semibold mb-4'>
          Choose Your Test
        </Text>

        <View className='gap-4'>
          {mockTestCategories.map((test, index) => (
            <MotiView
              key={test.id}
              from={{ opacity: 0, translateY: 20 }}
              animate={{ opacity: 1, translateY: 0 }}
              transition={{
                type: 'spring',
                damping: 15,
                delay: index * 100
              }}
            >
              <Pressable
                onPress={() => handleTestSelect(test)}
                className='bg-bg-200 border border-bg-300 rounded-xl p-4 active:opacity-80'
              >
                <View className='flex-row items-start justify-between mb-3'>
                  <View className='flex-row items-center flex-1'>
                    <Text className='text-2xl mr-3'>{test.icon}</Text>
                    <View className='flex-1'>
                      <Text className='text-text-100 font-semibold text-lg'>
                        {test.name}
                      </Text>
                      <Text className='text-text-200 text-sm mt-1'>
                        {test.description}
                      </Text>
                    </View>
                  </View>

                  <View
                    className='px-2 py-1 rounded-full'
                    style={{ backgroundColor: getDifficultyColor(test.difficulty) + '20' }}
                  >
                    <Text
                      className='text-xs font-semibold capitalize'
                      style={{ color: getDifficultyColor(test.difficulty) }}
                    >
                      {test.difficulty}
                    </Text>
                  </View>
                </View>

                <View className='flex-row items-center justify-between mb-3'>
                  <View className='flex-row items-center gap-4'>
                    <View className='flex-row items-center'>
                      <Clock size={14} color="#e0e0e0" />
                      <Text className='text-text-200 text-xs ml-1'>
                        {formatDuration(test.duration)}
                      </Text>
                    </View>
                    <View className='flex-row items-center'>
                      <FileText size={14} color="#e0e0e0" />
                      <Text className='text-text-200 text-xs ml-1'>
                        {test.questionCount} questions
                      </Text>
                    </View>
                    <View className='flex-row items-center'>
                      <Target size={14} color="#e0e0e0" />
                      <Text className='text-text-200 text-xs ml-1'>
                        {test.passingScore}% to pass
                      </Text>
                    </View>
                  </View>
                </View>

                <View className='flex-row items-center justify-between'>
                  <View className='flex-row items-center gap-4'>
                    <Text className='text-text-200 text-xs'>
                      Attempts: {test.attempts}
                    </Text>
                    <Text className='text-text-200 text-xs'>
                      Best: <Text style={{ color: test.color }}>{test.bestScore}%</Text>
                    </Text>
                    <Text className='text-text-200 text-xs'>
                      Avg: {test.averageScore}%
                    </Text>
                  </View>
                  <ChevronRight size={16} color="#e0e0e0" />
                </View>
              </Pressable>
            </MotiView>
          ))}
        </View>
      </View>

      {/* Recent Tests */}
      <View className='px-6 mb-6'>
        <Collapsible
          title="Recent Test Results"
          icon={<BarChart3 size={20} color="#e0e0e0" />}
          defaultExpanded={false}
        >
          <View className='gap-3'>
            {mockRecentTests.map((test, index) => (
              <MotiView
                key={test.id}
                from={{ opacity: 0, translateX: -20 }}
                animate={{ opacity: 1, translateX: 0 }}
                transition={{
                  type: 'spring',
                  damping: 15,
                  delay: index * 100
                }}
              >
                <View className='bg-bg-300/50 rounded-lg p-3'>
                  <View className='flex-row items-center justify-between mb-2'>
                    <Text className='text-text-100 font-semibold'>
                      {test.category}
                    </Text>
                    <View className='flex-row items-center'>
                      {test.passed ? (
                        <CheckCircle size={16} color="#10B981" />
                      ) : (
                        <AlertCircle size={16} color="#EF4444" />
                      )}
                      <Text
                        className='text-sm font-bold ml-1'
                        style={{
                          color: test.passed ? '#10B981' : '#EF4444'
                        }}
                      >
                        {test.score}%
                      </Text>
                    </View>
                  </View>
                  <View className='flex-row items-center justify-between'>
                    <Text className='text-text-200 text-xs'>
                      {test.correctAnswers}/{test.totalQuestions} correct • {test.duration}
                    </Text>
                    <Text className='text-text-200 text-xs'>
                      {test.completedAt}
                    </Text>
                  </View>
                </View>
              </MotiView>
            ))}
          </View>
        </Collapsible>
      </View>

      {/* Performance Insights */}
      <View className='px-6 mb-6'>
        <Collapsible
          title="Performance Insights"
          icon={<TrendingUp size={20} color="#00FFFF" />}
          defaultExpanded={false}
        >
          <View className='gap-3'>
            <View className='bg-bg-300/50 rounded-lg p-3'>
              <View className='flex-row items-center mb-2'>
                <Award size={16} color="#FFD700" />
                <Text className='text-text-100 font-semibold ml-2'>
                  Strongest Area
                </Text>
              </View>
              <Text className='text-text-200 text-sm'>
                Anatomy & Physiology (92% average)
              </Text>
            </View>

            <View className='bg-bg-300/50 rounded-lg p-3'>
              <View className='flex-row items-center mb-2'>
                <Target size={16} color="#F59E0B" />
                <Text className='text-text-100 font-semibold ml-2'>
                  Needs Improvement
                </Text>
              </View>
              <Text className='text-text-200 text-sm'>
                Clinical Scenarios (68% average)
              </Text>
            </View>

            <View className='bg-bg-300/50 rounded-lg p-3'>
              <View className='flex-row items-center mb-2'>
                <TrendingUp size={16} color="#10B981" />
                <Text className='text-text-100 font-semibold ml-2'>
                  Progress Trend
                </Text>
              </View>
              <Text className='text-text-200 text-sm'>
                +12% improvement over last month
              </Text>
            </View>
          </View>
        </Collapsible>
      </View>
    </ScrollView>
  );

  const renderTestSetup = () => (
    <ScrollView
      className='flex-1'
      showsVerticalScrollIndicator={false}
      contentContainerStyle={{ paddingBottom: 100 }}
    >
      {/* Header */}
      <MotiView
        from={{ opacity: 0, translateY: -30 }}
        animate={{ opacity: 1, translateY: 0 }}
        transition={{ type: 'spring', damping: 15 }}
        className='flex-row items-center justify-between px-6 pt-16 pb-6'
      >
        <Button
          onPress={handleBackToSelection}
          variant="ghost"
          size="sm"
          className='p-2'
        >
          <ArrowLeft size={24} color="#e0e0e0" />
        </Button>

        <Text className='text-text-100 text-xl font-bold'>Test Setup</Text>

        <View className='w-10' />
      </MotiView>

      {/* Progress Steps */}
      <View className='px-6 mb-6'>
        <StepperBreadcrumb
          steps={[
            { label: 'Select', completed: true },
            { label: 'Setup', active: true },
            { label: 'Test', completed: false },
            { label: 'Results', completed: false },
          ]}
        />
      </View>

      {/* Selected Test Info */}
      <View className='px-6 mb-6'>
        <MotiView
          from={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ type: 'spring', damping: 15, delay: 200 }}
        >
          <AnimatedCard
            title={selectedTest?.name || ''}
            description={selectedTest?.description || ''}
            variant="primary"
            className='p-4'
          >
            <View className='flex-row items-center justify-between mt-3'>
              <View className='flex-row items-center gap-4'>
                <View className='flex-row items-center'>
                  <Clock size={16} color="#e0e0e0" />
                  <Text className='text-text-200 text-sm ml-1'>
                    {formatDuration(selectedTest?.duration || 0)}
                  </Text>
                </View>
                <View className='flex-row items-center'>
                  <FileText size={16} color="#e0e0e0" />
                  <Text className='text-text-200 text-sm ml-1'>
                    {selectedTest?.questionCount} questions
                  </Text>
                </View>
              </View>
              <Text className='text-3xl'>{selectedTest?.icon}</Text>
            </View>
          </AnimatedCard>
        </MotiView>
      </View>

      {/* Test Instructions */}
      <View className='px-6 mb-6'>
        <Text className='text-text-100 text-lg font-semibold mb-4'>
          Test Instructions
        </Text>

        <AnimatedCard
          title=""
          variant="default"
          className='p-4'
        >
          <View className='gap-3'>
            <View className='flex-row items-start'>
              <View className='w-2 h-2 bg-primary-100 rounded-full mt-2 mr-3' />
              <Text className='text-text-200 text-sm flex-1'>
                You have {formatDuration(selectedTest?.duration || 0)} to complete {selectedTest?.questionCount} questions
              </Text>
            </View>
            <View className='flex-row items-start'>
              <View className='w-2 h-2 bg-primary-100 rounded-full mt-2 mr-3' />
              <Text className='text-text-200 text-sm flex-1'>
                You need {selectedTest?.passingScore}% or higher to pass this test
              </Text>
            </View>
            <View className='flex-row items-start'>
              <View className='w-2 h-2 bg-primary-100 rounded-full mt-2 mr-3' />
              <Text className='text-text-200 text-sm flex-1'>
                You can review and change your answers before submitting
              </Text>
            </View>
            <View className='flex-row items-start'>
              <View className='w-2 h-2 bg-primary-100 rounded-full mt-2 mr-3' />
              <Text className='text-text-200 text-sm flex-1'>
                The test will auto-submit when time expires
              </Text>
            </View>
          </View>
        </AnimatedCard>
      </View>

      {/* Test Settings */}
      <View className='px-6 mb-6'>
        <Text className='text-text-100 text-lg font-semibold mb-4'>
          Test Settings
        </Text>

        <AnimatedCard
          title=""
          variant="default"
          className='p-4'
        >
          <View className='gap-4'>
            <View className='flex-row items-center justify-between'>
              <View className='flex-row items-center'>
                <Timer size={16} color="#e0e0e0" />
                <Text className='text-text-200 ml-2'>Show Timer</Text>
              </View>
              <Button
                onPress={() => setTestSettings(prev => ({
                  ...prev,
                  showTimer: !prev.showTimer
                }))}
                variant="ghost"
                size="sm"
                className={`w-12 h-6 rounded-full ${
                  testSettings.showTimer ? 'bg-primary-100' : 'bg-bg-300'
                }`}
              >
                <View
                  className={`w-4 h-4 rounded-full bg-white transition-transform ${
                    testSettings.showTimer ? 'translate-x-3' : '-translate-x-3'
                  }`}
                />
              </Button>
            </View>

            <View className='flex-row items-center justify-between'>
              <View className='flex-row items-center'>
                <CheckCircle size={16} color="#e0e0e0" />
                <Text className='text-text-200 ml-2'>Allow Review</Text>
              </View>
              <Button
                onPress={() => setTestSettings(prev => ({
                  ...prev,
                  allowReview: !prev.allowReview
                }))}
                variant="ghost"
                size="sm"
                className={`w-12 h-6 rounded-full ${
                  testSettings.allowReview ? 'bg-primary-100' : 'bg-bg-300'
                }`}
              >
                <View
                  className={`w-4 h-4 rounded-full bg-white transition-transform ${
                    testSettings.allowReview ? 'translate-x-3' : '-translate-x-3'
                  }`}
                />
              </Button>
            </View>

            <View className='flex-row items-center justify-between'>
              <View className='flex-row items-center'>
                <Clock size={16} color="#e0e0e0" />
                <Text className='text-text-200 ml-2'>Auto Submit</Text>
              </View>
              <Button
                onPress={() => setTestSettings(prev => ({
                  ...prev,
                  autoSubmit: !prev.autoSubmit
                }))}
                variant="ghost"
                size="sm"
                className={`w-12 h-6 rounded-full ${
                  testSettings.autoSubmit ? 'bg-primary-100' : 'bg-bg-300'
                }`}
              >
                <View
                  className={`w-4 h-4 rounded-full bg-white transition-transform ${
                    testSettings.autoSubmit ? 'translate-x-3' : '-translate-x-3'
                  }`}
                />
              </Button>
            </View>
          </View>
        </AnimatedCard>
      </View>

      {/* Start Test Button */}
      <View className='px-6 mb-6'>
        <MotiView
          from={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ type: 'spring', damping: 15, delay: 600 }}
        >
          <Button
            onPress={handleStartTest}
            className='w-full bg-primary-100 py-4'
          >
            <Play size={20} color="#FFFFFF" />
            <Text className='text-text-100 font-semibold text-lg ml-2'>
              Start Test
            </Text>
          </Button>
        </MotiView>
      </View>
    </ScrollView>
  );

  const renderActiveTest = () => (
    <View className='flex-1 justify-center items-center'>
      <MotiView
        from={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ type: 'spring', damping: 15 }}
        className='items-center'
      >
        <FileText size={64} color="#FF6B6B" />
        <Text className='text-text-100 text-2xl font-bold mt-4'>
          Test Starting...
        </Text>
        <Text className='text-text-200 text-center mt-2 max-w-sm'>
          Get ready for {selectedTest?.questionCount} questions on {selectedTest?.name}
        </Text>
        <Text className='text-text-200 text-center mt-2'>
          Time limit: {formatDuration(selectedTest?.duration || 0)}
        </Text>
      </MotiView>
    </View>
  );

  return (
    <View className='flex-1 bg-bg-100'>
      <StatusBar barStyle="light-content" backgroundColor="#0F0F0F" />

      {mode === 'selection' && renderTestSelection()}
      {mode === 'setup' && renderTestSetup()}
      {mode === 'active' && renderActiveTest()}

      {/* Bottom Navigation */}
      <BottomTabs />
    </View>
  );
}
