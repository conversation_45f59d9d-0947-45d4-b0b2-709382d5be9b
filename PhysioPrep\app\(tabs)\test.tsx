import * as React from 'react';
import { View, ScrollView, Pressable } from 'react-native';
import { useRouter } from 'expo-router';
import { <PERSON><PERSON><PERSON>ie<PERSON> } from 'moti';
import {
  ArrowLeft,
  Clock,
  FileText,
  Target,
  Filter,
  Play,
  Trophy,
  BarChart3,
  ChevronRight,
  Timer,
  CheckCircle,
  AlertCircle,
  Award,
  TrendingUp,
  Calendar,
  Users
} from 'lucide-react-native';
import { Text } from '~/components/ui/text';
import { But<PERSON> } from '~/components/ui/button';
import { AnimatedCard, StatsCard } from '~/components/ui/animated-card';
import { GridLayout, ResponsiveGrid } from '~/components/ui/grid-layout';
import { Collapsible } from '~/components/ui/collapsible';
import { StepperBreadcrumb } from '~/components/navigation/breadcrumb';

// Mock data for demonstration
const mockTestCategories = [
  {
    id: '1',
    name: 'Comprehensive Exam',
    description: 'Full-length practice exam covering all topics',
    duration: 180, // minutes
    questionCount: 150,
    passingScore: 70,
    difficulty: 'mixed',
    icon: '📋',
    color: '#FF6B6B',
    attempts: 3,
    bestScore: 85,
    averageScore: 78,
  },
  {
    id: '2',
    name: 'Anatomy Focus',
    description: 'Specialized test focusing on anatomical knowledge',
    duration: 90,
    questionCount: 75,
    passingScore: 75,
    difficulty: 'medium',
    icon: '🦴',
    color: '#00FFFF',
    attempts: 5,
    bestScore: 92,
    averageScore: 84,
  },
  {
    id: '3',
    name: 'Clinical Scenarios',
    description: 'Case-based questions for practical application',
    duration: 120,
    questionCount: 60,
    passingScore: 80,
    difficulty: 'hard',
    icon: '🏥',
    color: '#10B981',
    attempts: 2,
    bestScore: 76,
    averageScore: 72,
  },
  {
    id: '4',
    name: 'Quick Assessment',
    description: 'Short test for rapid knowledge check',
    duration: 30,
    questionCount: 25,
    passingScore: 70,
    difficulty: 'easy',
    icon: '⚡',
    color: '#F59E0B',
    attempts: 8,
    bestScore: 96,
    averageScore: 89,
  },
];

const mockRecentTests = [
  {
    id: '1',
    category: 'Comprehensive Exam',
    score: 85,
    passed: true,
    totalQuestions: 150,
    correctAnswers: 128,
    completedAt: '2 days ago',
    duration: '2h 45m',
    timeSpent: 165,
  },
  {
    id: '2',
    category: 'Anatomy Focus',
    score: 92,
    passed: true,
    totalQuestions: 75,
    correctAnswers: 69,
    completedAt: '1 week ago',
    duration: '1h 15m',
    timeSpent: 75,
  },
  {
    id: '3',
    category: 'Clinical Scenarios',
    score: 68,
    passed: false,
    totalQuestions: 60,
    correctAnswers: 41,
    completedAt: '2 weeks ago',
    duration: '1h 52m',
    timeSpent: 112,
  },
];

type TestMode = 'selection' | 'setup' | 'active' | 'results';

export default function TestScreen() {
  const router = useRouter();
  const [mode, setMode] = React.useState<TestMode>('selection');
  const [selectedTest, setSelectedTest] = React.useState<any>(null);
  const [testSettings, setTestSettings] = React.useState({
    showTimer: true,
    allowReview: true,
    autoSubmit: true,
    shuffleQuestions: false,
  });

  const handleTestSelect = (test: any) => {
    setSelectedTest(test);
    setMode('setup');
  };

  const handleStartTest = () => {
    setMode('active');
    // Here you would start the actual test
  };

  const handleBackToSelection = () => {
    setMode('selection');
    setSelectedTest(null);
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return '#10B981';
      case 'medium': return '#F59E0B';
      case 'hard': return '#EF4444';
      case 'mixed': return '#8B5CF6';
      default: return '#6B7280';
    }
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  const renderTestSelection = () => (
    <ScrollView
      className='flex-1'
      showsVerticalScrollIndicator={false}
      contentContainerStyle={{ paddingBottom: 100 }}
    >
      {/* Header */}
      <MotiView
        from={{ opacity: 0, translateY: -30 }}
        animate={{ opacity: 1, translateY: 0 }}
        transition={{ type: 'spring', damping: 15 }}
        className='flex-row items-center justify-between px-6 pt-16 pb-6'
      >
        <Button
          onPress={() => router.back()}
          variant="ghost"
          size="sm"
          className='p-2'
        >
          <ArrowLeft size={24} color="#e0e0e0" />
        </Button>

        <Text className='text-text-100 text-xl font-bold'>Practice Tests</Text>

        <Button
          onPress={() => {/* Filter functionality */}}
          variant="ghost"
          size="sm"
          className='p-2'
        >
          <Filter size={24} color="#e0e0e0" />
        </Button>
      </MotiView>

      {/* Quick Stats */}
      <View className='px-6 mb-6'>
        <GridLayout columns={3} spacing={12} animated={true}>
          <StatsCard
            title="Tests Taken"
            value="18"
            icon={<FileText size={16} color="#FF6B6B" />}
          />
          <StatsCard
            title="Pass Rate"
            value="89%"
            icon={<Target size={16} color="#10B981" />}
          />
          <StatsCard
            title="Avg Score"
            value="84%"
            icon={<Trophy size={16} color="#FFD700" />}
          />
        </GridLayout>
      </View>

      {/* Test Categories */}
      <View className='px-6 mb-6'>
        <Text className='text-text-100 text-lg font-semibold mb-4'>
          Choose Your Test
        </Text>

        <View className='gap-4'>
          {mockTestCategories.map((test, index) => (
            <MotiView
              key={test.id}
              from={{ opacity: 0, translateY: 20 }}
              animate={{ opacity: 1, translateY: 0 }}
              transition={{
                type: 'spring',
                damping: 15,
                delay: index * 100
              }}
            >
              <Pressable
                onPress={() => handleTestSelect(test)}
                className='bg-bg-200 border border-bg-300 rounded-xl p-4 active:opacity-80'
              >
                <View className='flex-row items-start justify-between mb-3'>
                  <View className='flex-row items-center flex-1'>
                    <Text className='text-2xl mr-3'>{test.icon}</Text>
                    <View className='flex-1'>
                      <Text className='text-text-100 font-semibold text-lg'>
                        {test.name}
                      </Text>
                      <Text className='text-text-200 text-sm mt-1'>
                        {test.description}
                      </Text>
                    </View>
                  </View>

                  <View
                    className='px-2 py-1 rounded-full'
                    style={{ backgroundColor: getDifficultyColor(test.difficulty) + '20' }}
                  >
                    <Text
                      className='text-xs font-semibold capitalize'
                      style={{ color: getDifficultyColor(test.difficulty) }}
                    >
                      {test.difficulty}
                    </Text>
                  </View>
                </View>

                <View className='flex-row items-center justify-between mb-3'>
                  <View className='flex-row items-center gap-4'>
                    <View className='flex-row items-center'>
                      <Clock size={14} color="#e0e0e0" />
                      <Text className='text-text-200 text-xs ml-1'>
                        {formatDuration(test.duration)}
                      </Text>
                    </View>
                    <View className='flex-row items-center'>
                      <FileText size={14} color="#e0e0e0" />
                      <Text className='text-text-200 text-xs ml-1'>
                        {test.questionCount} questions
                      </Text>
                    </View>
                    <View className='flex-row items-center'>
                      <Target size={14} color="#e0e0e0" />
                      <Text className='text-text-200 text-xs ml-1'>
                        {test.passingScore}% to pass
                      </Text>
                    </View>
                  </View>
                </View>

                <View className='flex-row items-center justify-between'>
                  <View className='flex-row items-center gap-4'>
                    <Text className='text-text-200 text-xs'>
                      Attempts: {test.attempts}
                    </Text>
                    <Text className='text-text-200 text-xs'>
                      Best: <Text style={{ color: test.color }}>{test.bestScore}%</Text>
                    </Text>
                    <Text className='text-text-200 text-xs'>
                      Avg: {test.averageScore}%
                    </Text>
                  </View>
                  <ChevronRight size={16} color="#e0e0e0" />
                </View>
              </Pressable>
            </MotiView>
          ))}
        </View>
      </View>
    </ScrollView>
  );

  const renderActiveTest = () => (
    <View className='flex-1 justify-center items-center'>
      <MotiView
        from={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ type: 'spring', damping: 15 }}
        className='items-center'
      >
        <FileText size={64} color="#FF6B6B" />
        <Text className='text-text-100 text-2xl font-bold mt-4'>
          Test Starting...
        </Text>
        <Text className='text-text-200 text-center mt-2 max-w-sm'>
          Get ready for {selectedTest?.questionCount} questions on {selectedTest?.name}
        </Text>
        <Text className='text-text-200 text-center mt-2'>
          Time limit: {formatDuration(selectedTest?.duration || 0)}
        </Text>
      </MotiView>
    </View>
  );

  return (
    <View className='flex-1 bg-bg-100'>
      {mode === 'selection' && renderTestSelection()}
      {mode === 'active' && renderActiveTest()}
    </View>
  );
}
