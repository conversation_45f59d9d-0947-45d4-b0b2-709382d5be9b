import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';

export default function ModalLayout() {
  return (
    <>
      <StatusBar style="light" backgroundColor="#0F0F0F" />
      <Stack
        screenOptions={{
          headerShown: false,
          contentStyle: { backgroundColor: '#0F0F0F' },
          presentation: 'modal',
          animation: 'slide_from_bottom',
        }}
      >
        <Stack.Screen 
          name="edit-profile" 
          options={{
            title: 'Edit Profile',
          }}
        />
        <Stack.Screen 
          name="change-password" 
          options={{
            title: 'Change Password',
          }}
        />
        <Stack.Screen 
          name="notifications-settings" 
          options={{
            title: 'Notification Settings',
          }}
        />
        <Stack.Screen 
          name="premium" 
          options={{
            title: 'Upgrade to Premium',
          }}
        />
        <Stack.Screen 
          name="help" 
          options={{
            title: 'Help & Support',
          }}
        />
        <Stack.Screen 
          name="privacy" 
          options={{
            title: 'Privacy Policy',
          }}
        />
        <Stack.Screen 
          name="terms" 
          options={{
            title: 'Terms of Service',
          }}
        />
      </Stack>
    </>
  );
}
