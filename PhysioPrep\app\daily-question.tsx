import * as React from 'react';
import { View, ScrollView, StatusBar, Pressable } from 'react-native';
import { useRouter } from 'expo-router';
import { <PERSON><PERSON><PERSON>ie<PERSON> } from 'moti';
import {
  ArrowLeft,
  Clock,
  Users,
  Target,
  CheckCircle,
  XCircle,
  Calendar,
  Trophy,
  RotateCcw,
  Share2,
  Lightbulb
} from 'lucide-react-native';
import { Text } from '~/components/ui/text';
import { Button } from '~/components/ui/button';
import { AnimatedCard, StatsCard } from '~/components/ui/animated-card';
import { BottomTabs } from '~/components/navigation/bottom-tabs';
import { Progress } from '~/components/ui/progress';

// Mock data for demonstration
const mockDailyQuestion = {
  id: '1',
  date: new Date().toISOString(),
  tier: 'free' as const,
  isToday: true,
  hasAnswered: false,
  question: {
    id: 'q1',
    questionText: 'Which muscle is primarily responsible for shoulder abduction?',
    options: [
      { id: 'opt1', text: 'Deltoid muscle', isCorrect: true },
      { id: 'opt2', text: 'Pectoralis major', isCorrect: false },
      { id: 'opt3', text: 'Latissimus dorsi', isCorrect: false },
      { id: 'opt4', text: 'Biceps brachii', isCorrect: false },
    ],
    difficulty: 'medium' as const,
    category: 'Anatomy',
    subject: 'Musculoskeletal System',
    explanation: 'The deltoid muscle, particularly the middle fibers, is the primary muscle responsible for shoulder abduction. It originates from the lateral third of the clavicle, acromion, and spine of the scapula, and inserts into the deltoid tuberosity of the humerus.',
  },
  stats: {
    totalAttempts: 1247,
    correctAttempts: 892,
    uniqueUsers: 1247,
    accuracyPercentage: 72,
  },
};

export default function DailyQuestionScreen() {
  const router = useRouter();
  const [selectedOption, setSelectedOption] = React.useState<string | null>(null);
  const [hasSubmitted, setHasSubmitted] = React.useState(false);
  const [timeSpent, setTimeSpent] = React.useState(0);
  const [startTime] = React.useState(Date.now());

  React.useEffect(() => {
    const timer = setInterval(() => {
      setTimeSpent(Math.floor((Date.now() - startTime) / 1000));
    }, 1000);

    return () => clearInterval(timer);
  }, [startTime]);

  const handleOptionSelect = (optionId: string) => {
    if (!hasSubmitted) {
      setSelectedOption(optionId);
    }
  };

  const handleSubmit = () => {
    if (selectedOption && !hasSubmitted) {
      setHasSubmitted(true);
      // Here you would call the API to submit the answer
    }
  };

  const handleTryAgain = () => {
    setSelectedOption(null);
    setHasSubmitted(false);
    setTimeSpent(0);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return '#10B981';
      case 'medium': return '#F59E0B';
      case 'hard': return '#EF4444';
      default: return '#6B7280';
    }
  };

  const getOptionStyle = (option: any) => {
    if (!hasSubmitted) {
      return selectedOption === option.id
        ? 'border-primary-100 bg-primary-100/10'
        : 'border-bg-300 bg-bg-200';
    }

    if (option.isCorrect) {
      return 'border-green-400 bg-green-400/10';
    }

    if (selectedOption === option.id && !option.isCorrect) {
      return 'border-red-400 bg-red-400/10';
    }

    return 'border-bg-300 bg-bg-200 opacity-60';
  };

  const getOptionIcon = (option: any) => {
    if (!hasSubmitted) return null;

    if (option.isCorrect) {
      return <CheckCircle size={20} color="#10B981" />;
    }

    if (selectedOption === option.id && !option.isCorrect) {
      return <XCircle size={20} color="#EF4444" />;
    }

    return null;
  };

  const isCorrect = hasSubmitted && selectedOption &&
    mockDailyQuestion.question.options.find(opt => opt.id === selectedOption)?.isCorrect;

  return (
    <View className='flex-1 bg-bg-100'>
      <StatusBar barStyle="light-content" backgroundColor="#0F0F0F" />

      <ScrollView
        className='flex-1'
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 100 }}
      >
        {/* Header */}
        <MotiView
          from={{ opacity: 0, translateY: -30 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ type: 'spring', damping: 15 }}
          className='flex-row items-center justify-between px-6 pt-16 pb-6'
        >
          <Button
            onPress={() => router.back()}
            variant="ghost"
            size="sm"
            className='p-2'
          >
            <ArrowLeft size={24} color="#e0e0e0" />
          </Button>

          <View className='items-center'>
            <Text className='text-text-100 text-lg font-semibold'>Daily Question</Text>
            <Text className='text-text-200 text-sm'>
              {new Date().toLocaleDateString('en-US', {
                weekday: 'long',
                month: 'short',
                day: 'numeric'
              })}
            </Text>
          </View>

          <Button
            onPress={() => {/* Share functionality */}}
            variant="ghost"
            size="sm"
            className='p-2'
          >
            <Share2 size={24} color="#e0e0e0" />
          </Button>
        </MotiView>

        {/* Question Stats */}
        <View className='px-6 mb-6'>
          <MotiView
            from={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ type: 'spring', damping: 15, delay: 200 }}
          >
            <View className='flex-row gap-3'>
              <View className='flex-1'>
                <StatsCard
                  title="Attempts"
                  value={mockDailyQuestion.stats.totalAttempts.toLocaleString()}
                  icon={<Users size={16} color="#00FFFF" />}
                />
              </View>
              <View className='flex-1'>
                <StatsCard
                  title="Accuracy"
                  value={`${mockDailyQuestion.stats.accuracyPercentage}%`}
                  icon={<Target size={16} color="#FF6B6B" />}
                />
              </View>
              <View className='flex-1'>
                <StatsCard
                  title="Time"
                  value={formatTime(timeSpent)}
                  icon={<Clock size={16} color="#e0e0e0" />}
                />
              </View>
            </View>
          </MotiView>
        </View>

        {/* Question Card */}
        <View className='px-6 mb-6'>
          <MotiView
            from={{ opacity: 0, translateY: 20 }}
            animate={{ opacity: 1, translateY: 0 }}
            transition={{ type: 'spring', damping: 15, delay: 300 }}
          >
            <AnimatedCard
              title=""
              variant="default"
              className='p-6'
            >
              {/* Question Meta */}
              <View className='flex-row items-center justify-between mb-4'>
                <View className='flex-row items-center gap-2'>
                  <View
                    className='px-2 py-1 rounded-full'
                    style={{ backgroundColor: getDifficultyColor(mockDailyQuestion.question.difficulty) + '20' }}
                  >
                    <Text
                      className='text-xs font-semibold capitalize'
                      style={{ color: getDifficultyColor(mockDailyQuestion.question.difficulty) }}
                    >
                      {mockDailyQuestion.question.difficulty}
                    </Text>
                  </View>
                  <Text className='text-text-200 text-sm'>
                    {mockDailyQuestion.question.category}
                  </Text>
                </View>

                {mockDailyQuestion.tier === 'premium' && (
                  <View className='bg-accent-100/20 px-2 py-1 rounded-full'>
                    <Text className='text-accent-100 text-xs font-semibold'>PREMIUM</Text>
                  </View>
                )}
              </View>

              {/* Question Text */}
              <Text className='text-text-100 text-lg font-medium leading-7 mb-6'>
                {mockDailyQuestion.question.questionText}
              </Text>

              {/* Options */}
              <View className='gap-3'>
                {mockDailyQuestion.question.options.map((option, index) => (
                  <MotiView
                    key={option.id}
                    from={{ opacity: 0, translateX: -20 }}
                    animate={{ opacity: 1, translateX: 0 }}
                    transition={{
                      type: 'spring',
                      damping: 15,
                      delay: 400 + (index * 100)
                    }}
                  >
                    <Pressable
                      onPress={() => handleOptionSelect(option.id)}
                      disabled={hasSubmitted}
                      className={`border-2 rounded-xl p-4 ${getOptionStyle(option)}`}
                    >
                      <View className='flex-row items-center justify-between'>
                        <View className='flex-row items-center flex-1'>
                          <View className='w-6 h-6 rounded-full border-2 border-text-200 items-center justify-center mr-3'>
                            <Text className='text-text-200 text-sm font-semibold'>
                              {String.fromCharCode(65 + index)}
                            </Text>
                          </View>
                          <Text className='text-text-100 flex-1 leading-6'>
                            {option.text}
                          </Text>
                        </View>
                        {getOptionIcon(option)}
                      </View>
                    </Pressable>
                  </MotiView>
                ))}
              </View>
            </AnimatedCard>
          </MotiView>
        </View>

        {/* Submit Button */}
        {!hasSubmitted && (
          <View className='px-6 mb-6'>
            <MotiView
              from={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ type: 'spring', damping: 15, delay: 800 }}
            >
              <Button
                onPress={handleSubmit}
                disabled={!selectedOption}
                className={`w-full py-4 ${selectedOption ? 'bg-primary-100' : 'bg-bg-300'}`}
              >
                <Text className={`font-semibold text-lg ${selectedOption ? 'text-text-100' : 'text-text-200'}`}>
                  Submit Answer
                </Text>
              </Button>
            </MotiView>
          </View>
        )}

        {/* Result Section */}
        {hasSubmitted && (
          <View className='px-6 mb-6'>
            <MotiView
              from={{ opacity: 0, translateY: 20 }}
              animate={{ opacity: 1, translateY: 0 }}
              transition={{ type: 'spring', damping: 15 }}
            >
              <AnimatedCard
                title=""
                variant={isCorrect ? "success" : "danger"}
                className='p-6'
              >
                <View className='items-center mb-4'>
                  {isCorrect ? (
                    <CheckCircle size={48} color="#10B981" />
                  ) : (
                    <XCircle size={48} color="#EF4444" />
                  )}
                  <Text className={`text-xl font-bold mt-2 ${isCorrect ? 'text-green-400' : 'text-red-400'}`}>
                    {isCorrect ? 'Correct!' : 'Incorrect'}
                  </Text>
                  <Text className='text-text-200 text-center mt-1'>
                    {isCorrect
                      ? 'Great job! You got it right.'
                      : 'Don\'t worry, keep learning!'
                    }
                  </Text>
                </View>

                {/* Explanation */}
                <View className='bg-bg-300/50 rounded-lg p-4 mb-4'>
                  <View className='flex-row items-center mb-2'>
                    <Lightbulb size={20} color="#00FFFF" />
                    <Text className='text-accent-100 font-semibold ml-2'>Explanation</Text>
                  </View>
                  <Text className='text-text-200 leading-6'>
                    {mockDailyQuestion.question.explanation}
                  </Text>
                </View>

                {/* Action Buttons */}
                <View className='flex-row gap-3'>
                  <Button
                    onPress={handleTryAgain}
                    variant="outline"
                    className='flex-1 py-3'
                  >
                    <RotateCcw size={16} color="#e0e0e0" />
                    <Text className='text-text-200 ml-2'>Try Again</Text>
                  </Button>

                  <Button
                    onPress={() => router.push('/quiz')}
                    className='flex-1 bg-primary-100 py-3'
                  >
                    <Text className='text-text-100 font-semibold'>Practice More</Text>
                  </Button>
                </View>
              </AnimatedCard>
            </MotiView>
          </View>
        )}

        {/* Progress Indicator */}
        <View className='px-6 mb-6'>
          <MotiView
            from={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ type: 'timing', duration: 500, delay: 1000 }}
          >
            <AnimatedCard
              title="Daily Progress"
              variant="default"
              className='p-4'
            >
              <View className='flex-row items-center justify-between mb-2'>
                <Text className='text-text-200 text-sm'>Questions this week</Text>
                <Text className='text-text-100 font-semibold'>5/7</Text>
              </View>
              <Progress value={71} className='h-2' indicatorClassName='bg-primary-100' />
              <Text className='text-text-200 text-xs mt-2'>
                2 more questions to complete your weekly goal!
              </Text>
            </AnimatedCard>
          </MotiView>
        </View>
      </ScrollView>

      {/* Bottom Navigation */}
      <BottomTabs />
    </View>
  );
}
