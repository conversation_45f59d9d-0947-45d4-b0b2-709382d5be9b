import { useEffect } from 'react';
import { useRecoilState, useRecoilValue, useSetRecoilState } from 'recoil';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { 
  userState, 
  authTokenState, 
  isAuthenticatedState, 
  authLoadingState,
  User 
} from '../store/atoms';
import { authApi } from '../api/client';

export const useAuth = () => {
  const [user, setUser] = useRecoilState(userState);
  const [authToken, setAuthToken] = useRecoilState(authTokenState);
  const [isAuthenticated, setIsAuthenticated] = useRecoilState(isAuthenticatedState);
  const [authLoading, setAuthLoading] = useRecoilState(authLoadingState);

  // Initialize auth state from storage
  useEffect(() => {
    const initializeAuth = async () => {
      setAuthLoading(true);
      try {
        const [storedToken, storedUser] = await Promise.all([
          AsyncStorage.getItem('authToken'),
          AsyncStorage.getItem('user')
        ]);

        if (storedToken && storedUser) {
          const parsedUser = JSON.parse(storedUser);
          setAuthToken(storedToken);
          setUser(parsedUser);
          setIsAuthenticated(true);

          // Verify token is still valid
          try {
            const response = await authApi.getMe();
            if (response.success && response.data?.user) {
              setUser(response.data.user);
              await AsyncStorage.setItem('user', JSON.stringify(response.data.user));
            }
          } catch (error) {
            // Token is invalid, clear auth state
            await logout();
          }
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
        await logout();
      } finally {
        setAuthLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const login = async (email: string, password: string) => {
    setAuthLoading(true);
    try {
      const response = await authApi.login({ email, password });
      
      if (response.success && response.data) {
        const { token, user: userData } = response.data;
        
        // Store in state
        setAuthToken(token);
        setUser(userData);
        setIsAuthenticated(true);
        
        // Store in AsyncStorage
        await Promise.all([
          AsyncStorage.setItem('authToken', token),
          AsyncStorage.setItem('user', JSON.stringify(userData))
        ]);
        
        return { success: true, user: userData };
      } else {
        throw new Error(response.errors?.[0]?.msg || 'Login failed');
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.errors?.[0]?.msg || error.message || 'Login failed';
      return { success: false, error: errorMessage };
    } finally {
      setAuthLoading(false);
    }
  };

  const register = async (userData: {
    name: string;
    email: string;
    password: string;
    confirmPassword: string;
  }) => {
    setAuthLoading(true);
    try {
      const response = await authApi.register(userData);
      
      if (response.success && response.data) {
        const { token, user: newUser } = response.data;
        
        // Store in state
        setAuthToken(token);
        setUser(newUser);
        setIsAuthenticated(true);
        
        // Store in AsyncStorage
        await Promise.all([
          AsyncStorage.setItem('authToken', token),
          AsyncStorage.setItem('user', JSON.stringify(newUser))
        ]);
        
        return { success: true, user: newUser };
      } else {
        throw new Error(response.errors?.[0]?.msg || 'Registration failed');
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.errors?.[0]?.msg || error.message || 'Registration failed';
      return { success: false, error: errorMessage };
    } finally {
      setAuthLoading(false);
    }
  };

  const logout = async () => {
    try {
      // Clear state
      setUser(null);
      setAuthToken(null);
      setIsAuthenticated(false);
      
      // Clear AsyncStorage
      await Promise.all([
        AsyncStorage.removeItem('authToken'),
        AsyncStorage.removeItem('user')
      ]);
      
      return { success: true };
    } catch (error) {
      console.error('Error during logout:', error);
      return { success: false, error: 'Logout failed' };
    }
  };

  const updateProfile = async (profileData: {
    name?: string;
    preferences?: any;
  }) => {
    try {
      const response = await authApi.updateProfile(profileData);
      
      if (response.success && response.data?.user) {
        const updatedUser = response.data.user;
        setUser(updatedUser);
        await AsyncStorage.setItem('user', JSON.stringify(updatedUser));
        return { success: true, user: updatedUser };
      } else {
        throw new Error(response.errors?.[0]?.msg || 'Profile update failed');
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.errors?.[0]?.msg || error.message || 'Profile update failed';
      return { success: false, error: errorMessage };
    }
  };

  const changePassword = async (passwordData: {
    currentPassword: string;
    newPassword: string;
    confirmNewPassword: string;
  }) => {
    try {
      const response = await authApi.changePassword(passwordData);
      
      if (response.success) {
        return { success: true };
      } else {
        throw new Error(response.errors?.[0]?.msg || 'Password change failed');
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.errors?.[0]?.msg || error.message || 'Password change failed';
      return { success: false, error: errorMessage };
    }
  };

  const requestPasswordReset = async (email: string) => {
    try {
      const response = await authApi.requestPasswordReset(email);
      
      if (response.success) {
        return { success: true };
      } else {
        throw new Error(response.errors?.[0]?.msg || 'Password reset request failed');
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.errors?.[0]?.msg || error.message || 'Password reset request failed';
      return { success: false, error: errorMessage };
    }
  };

  const resetPassword = async (resetData: {
    token: string;
    password: string;
    confirmPassword: string;
  }) => {
    try {
      const response = await authApi.resetPassword(resetData);
      
      if (response.success && response.data?.token) {
        // Auto-login after password reset
        setAuthToken(response.data.token);
        await AsyncStorage.setItem('authToken', response.data.token);
        
        // Get user data
        const userResponse = await authApi.getMe();
        if (userResponse.success && userResponse.data?.user) {
          setUser(userResponse.data.user);
          setIsAuthenticated(true);
          await AsyncStorage.setItem('user', JSON.stringify(userResponse.data.user));
        }
        
        return { success: true };
      } else {
        throw new Error(response.errors?.[0]?.msg || 'Password reset failed');
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.errors?.[0]?.msg || error.message || 'Password reset failed';
      return { success: false, error: errorMessage };
    }
  };

  const verifyEmail = async (token: string) => {
    try {
      const response = await authApi.verifyEmail(token);
      
      if (response.success) {
        // Update user state to reflect email verification
        if (user) {
          const updatedUser = { ...user, isEmailVerified: true };
          setUser(updatedUser);
          await AsyncStorage.setItem('user', JSON.stringify(updatedUser));
        }
        return { success: true };
      } else {
        throw new Error(response.errors?.[0]?.msg || 'Email verification failed');
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.errors?.[0]?.msg || error.message || 'Email verification failed';
      return { success: false, error: errorMessage };
    }
  };

  const resendVerification = async () => {
    try {
      const response = await authApi.resendVerification();
      
      if (response.success) {
        return { success: true };
      } else {
        throw new Error(response.errors?.[0]?.msg || 'Failed to resend verification email');
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.errors?.[0]?.msg || error.message || 'Failed to resend verification email';
      return { success: false, error: errorMessage };
    }
  };

  return {
    // State
    user,
    authToken,
    isAuthenticated,
    authLoading,
    
    // Actions
    login,
    register,
    logout,
    updateProfile,
    changePassword,
    requestPasswordReset,
    resetPassword,
    verifyEmail,
    resendVerification,
  };
};
