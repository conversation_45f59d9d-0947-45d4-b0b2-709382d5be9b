import * as React from 'react';
import { View, ScrollView, Dimensions } from 'react-native';
import { <PERSON><PERSON>View } from 'moti';
import { cn } from '~/lib/utils';

interface GridLayoutProps {
  children: React.ReactNode[];
  columns?: number;
  spacing?: number;
  className?: string;
  scrollable?: boolean;
  animated?: boolean;
  staggerDelay?: number;
}

export function GridLayout({
  children,
  columns = 2,
  spacing = 16,
  className,
  scrollable = false,
  animated = true,
  staggerDelay = 100,
}: GridLayoutProps) {
  const screenWidth = Dimensions.get('window').width;
  const itemWidth = (screenWidth - (spacing * (columns + 1))) / columns;

  const renderGrid = () => (
    <View 
      className={cn('flex-row flex-wrap', className)}
      style={{ 
        paddingHorizontal: spacing / 2,
        marginHorizontal: -spacing / 2 
      }}
    >
      {children.map((child, index) => {
        const ItemWrapper = animated ? MotiView : View;
        const animationProps = animated ? {
          from: { opacity: 0, scale: 0.8, translateY: 20 },
          animate: { opacity: 1, scale: 1, translateY: 0 },
          transition: {
            type: 'spring',
            damping: 15,
            stiffness: 150,
            delay: index * staggerDelay,
          },
        } : {};

        return (
          <ItemWrapper
            key={index}
            {...animationProps}
            style={{
              width: itemWidth,
              marginHorizontal: spacing / 2,
              marginBottom: spacing,
            }}
          >
            {child}
          </ItemWrapper>
        );
      })}
    </View>
  );

  if (scrollable) {
    return (
      <ScrollView 
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingVertical: spacing }}
      >
        {renderGrid()}
      </ScrollView>
    );
  }

  return (
    <View style={{ paddingVertical: spacing }}>
      {renderGrid()}
    </View>
  );
}

interface MasonryGridProps {
  children: React.ReactNode[];
  columns?: number;
  spacing?: number;
  className?: string;
  animated?: boolean;
}

export function MasonryGrid({
  children,
  columns = 2,
  spacing = 16,
  className,
  animated = true,
}: MasonryGridProps) {
  const [columnHeights, setColumnHeights] = React.useState<number[]>(
    new Array(columns).fill(0)
  );
  
  const screenWidth = Dimensions.get('window').width;
  const itemWidth = (screenWidth - (spacing * (columns + 1))) / columns;

  const getShortestColumnIndex = () => {
    return columnHeights.indexOf(Math.min(...columnHeights));
  };

  return (
    <ScrollView 
      showsVerticalScrollIndicator={false}
      className={className}
      contentContainerStyle={{ paddingVertical: spacing }}
    >
      <View 
        style={{ 
          flexDirection: 'row',
          paddingHorizontal: spacing / 2,
          marginHorizontal: -spacing / 2 
        }}
      >
        {Array.from({ length: columns }, (_, columnIndex) => (
          <View
            key={columnIndex}
            style={{
              flex: 1,
              marginHorizontal: spacing / 2,
            }}
          >
            {children
              .filter((_, index) => index % columns === columnIndex)
              .map((child, index) => {
                const ItemWrapper = animated ? MotiView : View;
                const animationProps = animated ? {
                  from: { opacity: 0, scale: 0.9, translateY: 30 },
                  animate: { opacity: 1, scale: 1, translateY: 0 },
                  transition: {
                    type: 'spring',
                    damping: 15,
                    delay: index * 150,
                  },
                } : {};

                return (
                  <ItemWrapper
                    key={index}
                    {...animationProps}
                    style={{ marginBottom: spacing }}
                  >
                    {child}
                  </ItemWrapper>
                );
              })}
          </View>
        ))}
      </View>
    </ScrollView>
  );
}

interface ResponsiveGridProps {
  children: React.ReactNode[];
  minItemWidth?: number;
  maxColumns?: number;
  spacing?: number;
  className?: string;
  animated?: boolean;
}

export function ResponsiveGrid({
  children,
  minItemWidth = 150,
  maxColumns = 4,
  spacing = 16,
  className,
  animated = true,
}: ResponsiveGridProps) {
  const screenWidth = Dimensions.get('window').width;
  const availableWidth = screenWidth - (spacing * 2);
  const calculatedColumns = Math.min(
    Math.floor(availableWidth / minItemWidth),
    maxColumns
  );
  const columns = Math.max(calculatedColumns, 1);

  return (
    <GridLayout
      columns={columns}
      spacing={spacing}
      className={className}
      scrollable={true}
      animated={animated}
    >
      {children}
    </GridLayout>
  );
}
