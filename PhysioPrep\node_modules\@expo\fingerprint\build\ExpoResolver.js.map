{"version": 3, "file": "ExpoResolver.js", "sourceRoot": "", "sources": ["../src/ExpoResolver.ts"], "names": [], "mappings": ";;;;;AASA,gDAOC;AAKD,gDAUC;AAKD,8EAkBC;AAMD,sEAMC;AAKD,sEAOC;AAMD,gDAMC;AA1FD,gDAAwB;AACxB,gEAAuC;AACvC,oDAA4B;AAE5B,IAAI,gCAAgC,GAA4B,IAAI,CAAC;AAErE;;GAEG;AACH,SAAgB,kBAAkB,CAAC,WAAmB;IACpD,MAAM,mBAAmB,GAAG,sBAAW,CAAC,MAAM,CAAC,WAAW,EAAE,mBAAmB,CAAC,CAAC;IACjF,IAAI,mBAAmB,EAAE,CAAC;QACxB,MAAM,eAAe,GAAG,OAAO,CAAC,mBAAmB,CAAC,CAAC;QACrD,OAAO,eAAe,CAAC,OAAO,CAAC;IACjC,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;GAEG;AACH,SAAgB,kBAAkB,CAAC,WAAmB;IACpD,MAAM,eAAe,GAAG,sBAAW,CAAC,MAAM,CAAC,WAAW,EAAE,mBAAmB,CAAC,CAAC;IAC7E,MAAM,sBAAsB,GAAG,sBAAW,CAAC,MAAM,CAC/C,eAAe,IAAI,WAAW,EAC9B,wBAAwB,CACzB,CAAC;IACF,IAAI,sBAAsB,EAAE,CAAC;QAC3B,OAAO,cAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;IAC9C,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;GAEG;AACH,SAAgB,iCAAiC,CAAC,WAAmB;IACnE,IAAI,gCAAgC,EAAE,CAAC;QACrC,MAAM,CAAC,iBAAiB,EAAE,iBAAiB,CAAC,GAAG,gCAAgC,CAAC;QAChF,IAAI,iBAAiB,KAAK,WAAW,EAAE,CAAC;YACtC,OAAO,iBAAiB,CAAC;QAC3B,CAAC;IACH,CAAC;IACD,MAAM,eAAe,GAAG,sBAAW,CAAC,MAAM,CAAC,WAAW,EAAE,mBAAmB,CAAC,CAAC;IAC7E,MAAM,0BAA0B,GAAG,sBAAW,CAAC,MAAM,CACnD,eAAe,IAAI,WAAW,EAC9B,uCAAuC,CACxC,CAAC;IACF,IAAI,0BAA0B,EAAE,CAAC;QAC/B,MAAM,sBAAsB,GAAG,cAAI,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC;QACxE,gCAAgC,GAAG,CAAC,WAAW,EAAE,sBAAsB,CAAC,CAAC;QACzE,OAAO,sBAAsB,CAAC;IAChC,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;GAGG;AACH,SAAgB,6BAA6B,CAAC,WAAmB;IAC/D,MAAM,sBAAsB,GAAG,iCAAiC,CAAC,WAAW,CAAC,CAAC;IAC9E,IAAI,sBAAsB,IAAI,IAAI,EAAE,CAAC;QACnC,MAAM,IAAI,KAAK,CAAC,iEAAiE,CAAC,CAAC;IACrF,CAAC;IACD,OAAO,cAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,KAAK,EAAE,6BAA6B,CAAC,CAAC;AACjF,CAAC;AAED;;GAEG;AACH,SAAgB,6BAA6B,CAAC,WAAmB;IAC/D,MAAM,sBAAsB,GAAG,iCAAiC,CAAC,WAAW,CAAC,CAAC;IAC9E,IAAI,sBAAsB,EAAE,CAAC;QAC3B,MAAM,sBAAsB,GAAG,OAAO,CAAC,cAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,cAAc,CAAC,CAAC,CAAC;QAC1F,OAAO,sBAAsB,CAAC,OAAO,CAAC;IACxC,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;GAGG;AACH,SAAgB,kBAAkB,CAAC,WAAmB,EAAE,KAAa;IACnE,MAAM,WAAW,GAAG,kBAAkB,CAAC,WAAW,CAAC,CAAC;IACpD,IAAI,WAAW,EAAE,CAAC;QAChB,OAAO,gBAAM,CAAC,SAAS,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;IAC9C,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC"}