import * as React from 'react';
import { Pressable, View } from 'react-native';
import { MotiView } from 'moti';
import { ChevronDown } from 'lucide-react-native';
import { Text } from './text';
import { cn } from '~/lib/utils';

interface CollapsibleProps {
  title: string;
  children: React.ReactNode;
  defaultExpanded?: boolean;
  icon?: React.ReactNode;
  className?: string;
  headerClassName?: string;
  contentClassName?: string;
  disabled?: boolean;
}

export function Collapsible({
  title,
  children,
  defaultExpanded = false,
  icon,
  className,
  headerClassName,
  contentClassName,
  disabled = false,
}: CollapsibleProps) {
  const [isExpanded, setIsExpanded] = React.useState(defaultExpanded);

  const toggleExpanded = () => {
    if (!disabled) {
      setIsExpanded(!isExpanded);
    }
  };

  return (
    <View className={cn('border border-bg-300 rounded-lg overflow-hidden', className)}>
      <Pressable
        onPress={toggleExpanded}
        disabled={disabled}
        className={cn(
          'flex-row items-center justify-between p-4 bg-bg-200',
          disabled && 'opacity-60',
          headerClassName
        )}
      >
        <View className="flex-row items-center flex-1">
          {icon && (
            <View className="mr-3">
              {icon}
            </View>
          )}
          <Text className="text-text-100 font-semibold flex-1">
            {title}
          </Text>
        </View>
        
        <MotiView
          animate={{
            rotate: isExpanded ? '180deg' : '0deg',
          }}
          transition={{
            type: 'timing',
            duration: 200,
          }}
        >
          <ChevronDown size={20} color="#e0e0e0" />
        </MotiView>
      </Pressable>

      <MotiView
        animate={{
          height: isExpanded ? 'auto' : 0,
          opacity: isExpanded ? 1 : 0,
        }}
        transition={{
          type: 'timing',
          duration: 200,
        }}
        style={{ overflow: 'hidden' }}
      >
        <View className={cn('p-4 bg-bg-100 border-t border-bg-300', contentClassName)}>
          {children}
        </View>
      </MotiView>
    </View>
  );
}

interface AccordionProps {
  items: Array<{
    id: string;
    title: string;
    content: React.ReactNode;
    icon?: React.ReactNode;
  }>;
  allowMultiple?: boolean;
  className?: string;
}

export function Accordion({
  items,
  allowMultiple = false,
  className,
}: AccordionProps) {
  const [expandedItems, setExpandedItems] = React.useState<string[]>([]);

  const toggleItem = (id: string) => {
    if (allowMultiple) {
      setExpandedItems(prev =>
        prev.includes(id)
          ? prev.filter(item => item !== id)
          : [...prev, id]
      );
    } else {
      setExpandedItems(prev =>
        prev.includes(id) ? [] : [id]
      );
    }
  };

  return (
    <View className={cn('space-y-2', className)}>
      {items.map((item, index) => (
        <MotiView
          key={item.id}
          from={{ opacity: 0, translateY: 20 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{
            type: 'timing',
            duration: 200,
            delay: index * 50,
          }}
        >
          <View className="border border-bg-300 rounded-lg overflow-hidden">
            <Pressable
              onPress={() => toggleItem(item.id)}
              className="flex-row items-center justify-between p-4 bg-bg-200"
            >
              <View className="flex-row items-center flex-1">
                {item.icon && (
                  <View className="mr-3">
                    {item.icon}
                  </View>
                )}
                <Text className="text-text-100 font-semibold flex-1">
                  {item.title}
                </Text>
              </View>
              
              <MotiView
                animate={{
                  rotate: expandedItems.includes(item.id) ? '180deg' : '0deg',
                }}
                transition={{
                  type: 'timing',
                  duration: 200,
                }}
              >
                <ChevronDown size={20} color="#e0e0e0" />
              </MotiView>
            </Pressable>

            <MotiView
              animate={{
                height: expandedItems.includes(item.id) ? 'auto' : 0,
                opacity: expandedItems.includes(item.id) ? 1 : 0,
              }}
              transition={{
                type: 'timing',
                duration: 200,
              }}
              style={{ overflow: 'hidden' }}
            >
              <View className="p-4 bg-bg-100 border-t border-bg-300">
                {item.content}
              </View>
            </MotiView>
          </View>
        </MotiView>
      ))}
    </View>
  );
}
