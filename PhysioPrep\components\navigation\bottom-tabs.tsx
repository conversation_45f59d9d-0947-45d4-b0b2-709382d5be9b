import * as React from 'react';
import { View, Pressable, Dimensions } from 'react-native';
import { useRouter, usePathname } from 'expo-router';
import { <PERSON><PERSON><PERSON>iew } from 'moti';
import { 
  Home, 
  Calendar, 
  BookOpen, 
  FileText, 
  User,
  Trophy,
  BarChart3
} from 'lucide-react-native';
import { Text } from '../ui/text';
import { cn } from '~/lib/utils';

interface TabItem {
  name: string;
  label: string;
  icon: React.ComponentType<any>;
  route: string;
  badge?: number;
}

const tabs: TabItem[] = [
  {
    name: 'home',
    label: 'Home',
    icon: Home,
    route: '/',
  },
  {
    name: 'daily',
    label: 'Daily',
    icon: Calendar,
    route: '/daily-question',
  },
  {
    name: 'quiz',
    label: 'Quiz',
    icon: BookOpen,
    route: '/quiz',
  },
  {
    name: 'test',
    label: 'Test',
    icon: FileText,
    route: '/test',
  },
  {
    name: 'profile',
    label: 'Profile',
    icon: User,
    route: '/profile',
  },
];

interface BottomTabsProps {
  className?: string;
}

export function BottomTabs({ className }: BottomTabsProps) {
  const router = useRouter();
  const pathname = usePathname();
  const screenWidth = Dimensions.get('window').width;
  const tabWidth = screenWidth / tabs.length;

  const getActiveIndex = () => {
    const activeTab = tabs.findIndex(tab => 
      pathname === tab.route || 
      (tab.route !== '/' && pathname.startsWith(tab.route))
    );
    return activeTab >= 0 ? activeTab : 0;
  };

  const activeIndex = getActiveIndex();

  return (
    <View className={cn(
      'absolute bottom-0 left-0 right-0 bg-bg-200 border-t border-bg-300',
      'safe-area-bottom',
      className
    )}>
      {/* Active tab indicator */}
      <MotiView
        animate={{
          translateX: activeIndex * tabWidth,
        }}
        transition={{
          type: 'spring',
          damping: 20,
          stiffness: 150,
        }}
        style={{
          position: 'absolute',
          top: 0,
          width: tabWidth,
          height: 3,
          backgroundColor: '#FF6B6B',
        }}
      />

      <View className="flex-row">
        {tabs.map((tab, index) => {
          const isActive = index === activeIndex;
          const Icon = tab.icon;

          return (
            <Pressable
              key={tab.name}
              onPress={() => router.push(tab.route as any)}
              style={{ width: tabWidth }}
              className="items-center justify-center py-3 px-2"
            >
              <MotiView
                animate={{
                  scale: isActive ? 1.1 : 1,
                  translateY: isActive ? -2 : 0,
                }}
                transition={{
                  type: 'spring',
                  damping: 15,
                }}
                className="items-center"
              >
                <View className="relative">
                  <Icon
                    size={24}
                    color={isActive ? '#FF6B6B' : '#e0e0e0'}
                    strokeWidth={isActive ? 2.5 : 2}
                  />
                  {tab.badge && tab.badge > 0 && (
                    <MotiView
                      from={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="absolute -top-1 -right-1 bg-primary-100 rounded-full min-w-[16px] h-4 items-center justify-center"
                    >
                      <Text className="text-text-100 text-xs font-bold">
                        {tab.badge > 99 ? '99+' : tab.badge}
                      </Text>
                    </MotiView>
                  )}
                </View>
                
                <MotiView
                  animate={{
                    opacity: isActive ? 1 : 0.7,
                  }}
                  transition={{
                    type: 'timing',
                    duration: 200,
                  }}
                >
                  <Text 
                    className={cn(
                      'text-xs font-medium mt-1',
                      isActive ? 'text-primary-100' : 'text-text-200'
                    )}
                  >
                    {tab.label}
                  </Text>
                </MotiView>
              </MotiView>
            </Pressable>
          );
        })}
      </View>
    </View>
  );
}

interface FloatingBottomTabsProps {
  className?: string;
}

export function FloatingBottomTabs({ className }: FloatingBottomTabsProps) {
  const router = useRouter();
  const pathname = usePathname();

  const getActiveIndex = () => {
    const activeTab = tabs.findIndex(tab => 
      pathname === tab.route || 
      (tab.route !== '/' && pathname.startsWith(tab.route))
    );
    return activeTab >= 0 ? activeTab : 0;
  };

  const activeIndex = getActiveIndex();

  return (
    <MotiView
      from={{ translateY: 100, opacity: 0 }}
      animate={{ translateY: 0, opacity: 1 }}
      transition={{
        type: 'spring',
        damping: 20,
        stiffness: 100,
      }}
      className={cn(
        'absolute bottom-6 left-4 right-4 bg-bg-200/95 backdrop-blur-lg',
        'rounded-2xl border border-bg-300 shadow-lg',
        className
      )}
    >
      <View className="flex-row items-center py-2 px-2">
        {tabs.map((tab, index) => {
          const isActive = index === activeIndex;
          const Icon = tab.icon;

          return (
            <Pressable
              key={tab.name}
              onPress={() => router.push(tab.route as any)}
              className="flex-1 items-center justify-center py-3 px-2 rounded-xl"
            >
              <MotiView
                animate={{
                  backgroundColor: isActive ? '#FF6B6B20' : 'transparent',
                  scale: isActive ? 1.05 : 1,
                }}
                transition={{
                  type: 'spring',
                  damping: 15,
                }}
                className="items-center justify-center w-full py-2 rounded-xl"
              >
                <Icon
                  size={22}
                  color={isActive ? '#FF6B6B' : '#e0e0e0'}
                  strokeWidth={isActive ? 2.5 : 2}
                />
                {isActive && (
                  <MotiView
                    from={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{
                      type: 'timing',
                      duration: 200,
                    }}
                  >
                    <Text className="text-primary-100 text-xs font-semibold mt-1">
                      {tab.label}
                    </Text>
                  </MotiView>
                )}
              </MotiView>
            </Pressable>
          );
        })}
      </View>
    </MotiView>
  );
}
