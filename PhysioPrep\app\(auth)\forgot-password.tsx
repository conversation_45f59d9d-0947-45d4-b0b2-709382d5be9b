import * as React from 'react';
import { View, ScrollView, KeyboardAvoidingView, Platform } from 'react-native';
import { useRouter } from 'expo-router';
import { MotiView } from 'moti';
import { ArrowLeft, Mail, CheckCircle } from 'lucide-react-native';
import { useAuth } from '~/lib/hooks/useAuth';
import { Text } from '~/components/ui/text';
import { Button } from '~/components/ui/button';
import { AnimatedCard } from '~/components/ui/animated-card';

export default function ForgotPasswordScreen() {
  const router = useRouter();
  const { requestPasswordReset, authLoading } = useAuth();
  const [email, setEmail] = React.useState('');
  const [errors, setErrors] = React.useState<string[]>([]);
  const [success, setSuccess] = React.useState<string>('');

  const handleInputChange = (value: string) => {
    setEmail(value);
    setErrors([]);
    setSuccess('');
  };

  const validateForm = () => {
    const newErrors: string[] = [];

    if (!email.trim()) {
      newErrors.push('Email is required');
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.push('Please enter a valid email');
    }

    setErrors(newErrors);
    return newErrors.length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      const result = await requestPasswordReset(email);
      if (result?.success) {
        setSuccess('Password reset link sent to your email');
      } else {
        setErrors([result?.error || 'Failed to send reset link']);
      }
    } catch (error) {
      setErrors(['An unexpected error occurred']);
    }
  };

  return (
    <View className='flex-1 bg-bg-100'>
      <KeyboardAvoidingView 
        className='flex-1'
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView 
          className='flex-1'
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ flexGrow: 1 }}
        >
          {/* Header */}
          <MotiView
            from={{ opacity: 0, translateY: -30 }}
            animate={{ opacity: 1, translateY: 0 }}
            transition={{ type: 'spring', damping: 15 }}
            className='flex-row items-center justify-between px-6 pt-16 pb-8'
          >
            <Button
              onPress={() => router.back()}
              variant="ghost"
              size="sm"
              className='p-2'
            >
              <ArrowLeft size={24} color="#e0e0e0" />
            </Button>

            <Text className='text-2xl font-bold text-primary-100'>
              Reset Password
            </Text>

            <View className='w-10' />
          </MotiView>

          {/* Form Container */}
          <View className='flex-1 px-6'>
            <MotiView
              from={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ type: 'spring', damping: 15, delay: 200 }}
            >
              <AnimatedCard
                title=""
                className='mb-6'
                variant="default"
              >
                <View className='p-6'>
                  <Text className='text-text-200 text-center mb-6'>
                    Enter your email address and we'll send you a link to reset your password.
                  </Text>

                  {/* Success Message */}
                  {success && (
                    <MotiView
                      from={{ opacity: 0, translateY: -10 }}
                      animate={{ opacity: 1, translateY: 0 }}
                      className='flex-row items-center bg-green-500/20 border border-green-500/30 rounded-lg p-3 mb-4'
                    >
                      <CheckCircle size={20} color="#10B981" />
                      <Text className='text-green-400 ml-2 flex-1'>{success}</Text>
                    </MotiView>
                  )}

                  {/* Error Messages */}
                  {errors.length > 0 && (
                    <MotiView
                      from={{ opacity: 0, translateY: -10 }}
                      animate={{ opacity: 1, translateY: 0 }}
                      className='bg-red-500/20 border border-red-500/30 rounded-lg p-3 mb-4'
                    >
                      {errors.map((error, index) => (
                        <Text key={index} className='text-red-400 text-sm'>
                          • {error}
                        </Text>
                      ))}
                    </MotiView>
                  )}

                  {/* Form Fields */}
                  <View className='gap-4'>
                    <View>
                      <Text className='text-text-200 text-sm mb-2'>Email</Text>
                      <View className='flex-row items-center bg-bg-300 rounded-lg px-4 py-3'>
                        <Mail size={20} color="#e0e0e0" />
                        <Text
                          className='flex-1 ml-3 text-text-100'
                          // This would be a TextInput in a real implementation
                        >
                          {email || 'Enter your email'}
                        </Text>
                      </View>
                    </View>

                    {/* Submit Button */}
                    <Button
                      onPress={handleSubmit}
                      disabled={authLoading || !!success}
                      className='w-full bg-primary-100 py-4 mt-4'
                    >
                      <Text className='text-text-100 font-semibold text-lg'>
                        {authLoading ? 'Sending...' : success ? 'Link Sent' : 'Send Reset Link'}
                      </Text>
                    </Button>
                  </View>
                </View>
              </AnimatedCard>
            </MotiView>

            {/* Mode Switching */}
            <MotiView
              from={{ opacity: 0, translateY: 20 }}
              animate={{ opacity: 1, translateY: 0 }}
              transition={{ type: 'spring', damping: 15, delay: 400 }}
              className='items-center gap-4'
            >
              <Button
                onPress={() => router.push('/(auth)/login')}
                variant="ghost"
              >
                <Text className='text-text-200'>Back to Sign In</Text>
              </Button>
            </MotiView>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
}
