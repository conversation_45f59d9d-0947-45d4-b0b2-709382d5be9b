{"version": 3, "names": ["React", "useDismissedRouteError", "state", "nextDismissedKey", "setNextDismissedKey", "useState", "dismissedRouteName", "routes", "find", "route", "key", "name", "useEffect", "message", "console", "error"], "sourceRoot": "../../../src", "sources": ["utils/useDismissedRouteError.tsx"], "mappings": ";;AAIA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAO,SAASC,sBAAsBA,CACpCC,KAA0C,EAC1C;EACA,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGJ,KAAK,CAACK,QAAQ,CAC5D,IACF,CAAC;EAED,MAAMC,kBAAkB,GAAGH,gBAAgB,GACvCD,KAAK,CAACK,MAAM,CAACC,IAAI,CAAEC,KAAK,IAAKA,KAAK,CAACC,GAAG,KAAKP,gBAAgB,CAAC,EAAEQ,IAAI,GAClE,IAAI;EAERX,KAAK,CAACY,SAAS,CAAC,MAAM;IACpB,IAAIN,kBAAkB,EAAE;MACtB,MAAMO,OAAO,GACX,eAAeP,kBAAkB,+DAA+D,GAChG,6HAA6H,GAC7H,gJAAgJ;MAElJQ,OAAO,CAACC,KAAK,CAACF,OAAO,CAAC;IACxB;EACF,CAAC,EAAE,CAACP,kBAAkB,CAAC,CAAC;EAExB,OAAO;IAAEF;EAAoB,CAAC;AAChC", "ignoreList": []}