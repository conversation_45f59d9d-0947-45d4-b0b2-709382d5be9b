import * as React from 'react';
import { View, Pressable, ScrollView } from 'react-native';
import { useRouter } from 'expo-router';
import { MotiView } from 'moti';
import { ChevronRight, Home } from 'lucide-react-native';
import { Text } from '../ui/text';
import { cn } from '~/lib/utils';

interface BreadcrumbItem {
  label: string;
  route?: string;
  icon?: React.ReactNode;
}

interface BreadcrumbProps {
  items: BreadcrumbItem[];
  className?: string;
  showHome?: boolean;
  maxItems?: number;
}

export function Breadcrumb({
  items,
  className,
  showHome = true,
  maxItems = 4,
}: BreadcrumbProps) {
  const router = useRouter();

  const allItems = showHome 
    ? [{ label: 'Home', route: '/', icon: <Home size={16} color="#e0e0e0" /> }, ...items]
    : items;

  const displayItems = allItems.length > maxItems
    ? [
        allItems[0],
        { label: '...', route: undefined },
        ...allItems.slice(-maxItems + 2)
      ]
    : allItems;

  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      className={cn('flex-row', className)}
      contentContainerStyle={{ alignItems: 'center', paddingHorizontal: 16 }}
    >
      {displayItems.map((item, index) => (
        <MotiView
          key={`${item.label}-${index}`}
          from={{ opacity: 0, translateX: -10 }}
          animate={{ opacity: 1, translateX: 0 }}
          transition={{
            type: 'timing',
            duration: 200,
            delay: index * 50,
          }}
          className="flex-row items-center"
        >
          {index > 0 && (
            <ChevronRight 
              size={16} 
              color="#666" 
              className="mx-2"
            />
          )}
          
          {item.route && item.label !== '...' ? (
            <Pressable
              onPress={() => router.push(item.route as any)}
              className="flex-row items-center py-2 px-1 rounded-md active:bg-bg-300"
            >
              {item.icon && (
                <View className="mr-1">
                  {item.icon}
                </View>
              )}
              <Text className={cn(
                'text-sm',
                index === displayItems.length - 1 
                  ? 'text-text-100 font-semibold' 
                  : 'text-text-200'
              )}>
                {item.label}
              </Text>
            </Pressable>
          ) : (
            <View className="flex-row items-center py-2 px-1">
              {item.icon && (
                <View className="mr-1">
                  {item.icon}
                </View>
              )}
              <Text className={cn(
                'text-sm',
                item.label === '...' 
                  ? 'text-text-200' 
                  : index === displayItems.length - 1 
                    ? 'text-text-100 font-semibold' 
                    : 'text-text-200'
              )}>
                {item.label}
              </Text>
            </View>
          )}
        </MotiView>
      ))}
    </ScrollView>
  );
}

interface StepperBreadcrumbProps {
  steps: Array<{
    label: string;
    completed?: boolean;
    active?: boolean;
  }>;
  className?: string;
}

export function StepperBreadcrumb({
  steps,
  className,
}: StepperBreadcrumbProps) {
  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      className={cn('flex-row', className)}
      contentContainerStyle={{ alignItems: 'center', paddingHorizontal: 16 }}
    >
      {steps.map((step, index) => (
        <MotiView
          key={`${step.label}-${index}`}
          from={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{
            type: 'spring',
            damping: 15,
            delay: index * 100,
          }}
          className="flex-row items-center"
        >
          {index > 0 && (
            <View className="w-8 h-0.5 bg-bg-300 mx-2" />
          )}
          
          <View className="items-center">
            <MotiView
              animate={{
                backgroundColor: step.completed 
                  ? '#FF6B6B' 
                  : step.active 
                    ? '#00FFFF' 
                    : '#353535',
                scale: step.active ? 1.1 : 1,
              }}
              transition={{
                type: 'spring',
                damping: 15,
              }}
              className="w-8 h-8 rounded-full items-center justify-center mb-2"
            >
              <Text className={cn(
                'text-xs font-bold',
                step.completed || step.active ? 'text-bg-100' : 'text-text-200'
              )}>
                {index + 1}
              </Text>
            </MotiView>
            
            <Text className={cn(
              'text-xs text-center max-w-[60px]',
              step.active 
                ? 'text-accent-100 font-semibold' 
                : step.completed 
                  ? 'text-primary-100 font-medium'
                  : 'text-text-200'
            )}>
              {step.label}
            </Text>
          </View>
        </MotiView>
      ))}
    </ScrollView>
  );
}
