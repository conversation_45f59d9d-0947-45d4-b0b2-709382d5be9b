import * as React from 'react';
import { useRouter, useSegments } from 'expo-router';
import { useRecoilValue } from 'recoil';
import { isAuthenticatedState, authLoadingState } from '~/lib/store/atoms';
import { useAuth } from '~/lib/hooks/useAuth';

interface AuthProviderProps {
  children: React.ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const router = useRouter();
  const segments = useSegments();
  const isAuthenticated = useRecoilValue(isAuthenticatedState);
  const authLoading = useRecoilValue(authLoadingState);

  React.useEffect(() => {
    if (authLoading) return; // Don't redirect while loading

    const inAuthGroup = segments[0] === '(auth)';
    const inTabsGroup = segments[0] === '(tabs)';
    const inOnboardingGroup = segments[0] === '(onboarding)';

    if (!isAuthenticated) {
      // Redirect to auth if not authenticated and not already in auth flow
      if (!inAuthGroup && !inOnboardingGroup && segments[0] !== 'index') {
        router.replace('/(auth)/login');
      }
    } else {
      // Redirect to tabs if authenticated and in auth flow
      if (inAuthGroup) {
        router.replace('/(tabs)/home');
      }
    }
  }, [isAuthenticated, authLoading, segments]);

  return <>{children}</>;
}
