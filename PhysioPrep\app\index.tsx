import * as React from 'react';
import { View, ScrollView, StatusBar } from 'react-native';
import { useRouter } from 'expo-router';
import { useRecoilValue } from 'recoil';
import { MotiView } from 'moti';
import {
  Calendar,
  BookOpen,
  FileText,
  Trophy,
  Users,
  TrendingUp,
  Star,
  Play
} from 'lucide-react-native';
import { userState, isAuthenticatedState } from '~/lib/store/atoms';
import { useAuth } from '~/lib/hooks/useAuth';
import { Button } from '~/components/ui/button';
import { Text } from '~/components/ui/text';
import { AnimatedCard, StatsCard, ProgressCard } from '~/components/ui/animated-card';
import { FloatingActionButton } from '~/components/ui/floating-action-button';
import { GridLayout } from '~/components/ui/grid-layout';
import { BottomTabs } from '~/components/navigation/bottom-tabs';

export default function HomeScreen() {
  const router = useRouter();
  const user = useRecoilValue(userState);
  const isAuthenticated = useRecoilValue(isAuthenticatedState);
  const { authLoading } = useAuth();

  const handleGetStarted = () => {
    if (isAuthenticated) {
      router.push('/dashboard');
    } else {
      router.push('/auth');
    }
  };

  const handleDailyQuestion = () => {
    router.push('/daily-question');
  };

  const handleQuiz = () => {
    router.push('/quiz');
  };

  const handleTest = () => {
    router.push('/test');
  };

  if (authLoading) {
    return (
      <View className='flex-1 justify-center items-center bg-bg-100'>
        <MotiView
          from={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ type: 'spring', damping: 15 }}
        >
          <Text className='text-text-100 text-lg'>Loading...</Text>
        </MotiView>
      </View>
    );
  }

  return (
    <View className='flex-1 bg-bg-100'>
      <StatusBar barStyle="light-content" backgroundColor="#0F0F0F" />

      <ScrollView
        className='flex-1'
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 100 }}
      >
        {/* Hero Section */}
        <MotiView
          from={{ opacity: 0, translateY: -50 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ type: 'spring', damping: 15, delay: 200 }}
          className='items-center px-6 pt-16 pb-8'
        >
          <Text className='text-5xl font-bold text-primary-100 text-center mb-2'>
            PhysioPrep
          </Text>
          <Text className='text-xl text-text-200 text-center mb-4'>
            Master Your Physiotherapy Exams
          </Text>
          <Text className='text-base text-text-200 text-center leading-6 max-w-sm'>
            Comprehensive exam preparation with daily questions, practice quizzes,
            and full-length tests designed to help you succeed.
          </Text>
        </MotiView>

        {/* Quick Stats */}
        <View className='px-6 mb-8'>
          <GridLayout columns={3} spacing={12} animated={true}>
            <StatsCard
              title="Questions"
              value="1000+"
              icon={<BookOpen size={20} color="#FF6B6B" />}
            />
            <StatsCard
              title="Topics"
              value="50+"
              icon={<FileText size={20} color="#00FFFF" />}
            />
            <StatsCard
              title="Pass Rate"
              value="95%"
              icon={<Trophy size={20} color="#FF6B6B" />}
            />
          </GridLayout>
        </View>

        {/* User Progress (if authenticated) */}
        {isAuthenticated && user && (
          <View className='px-6 mb-8'>
            <MotiView
              from={{ opacity: 0, translateX: -50 }}
              animate={{ opacity: 1, translateX: 0 }}
              transition={{ type: 'spring', damping: 15, delay: 400 }}
            >
              <Text className='text-text-100 text-lg font-semibold mb-4'>
                Welcome back, {user.name}! 👋
              </Text>
              <ProgressCard
                title="Study Progress"
                progress={user.stats.correctAnswers}
                total={user.stats.totalQuestionsAnswered}
                description={`${user.stats.currentStreak} day streak`}
                color="#FF6B6B"
              />
            </MotiView>
          </View>
        )}

        {/* Feature Cards */}
        <View className='px-6 mb-8'>
          <Text className='text-text-100 text-xl font-semibold mb-4'>
            Start Learning
          </Text>

          <View className='gap-4'>
            <AnimatedCard
              title="Daily Question"
              description="Challenge yourself with today's question"
              icon={<Calendar size={24} color="#00FFFF" />}
              onPress={handleDailyQuestion}
              variant="accent"
            />

            <AnimatedCard
              title="Practice Quiz"
              description="Quick quizzes on specific topics"
              icon={<BookOpen size={24} color="#FF6B6B" />}
              onPress={handleQuiz}
              variant="primary"
            />

            <AnimatedCard
              title="Full Test"
              description="Comprehensive exam simulation"
              icon={<FileText size={24} color="#e0e0e0" />}
              onPress={handleTest}
              variant="default"
            />
          </View>
        </View>

        {/* CTA Section */}
        <View className='px-6 mb-8'>
          {isAuthenticated ? (
            <MotiView
              from={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ type: 'spring', damping: 15, delay: 600 }}
            >
              <Button
                onPress={handleGetStarted}
                className='w-full bg-primary-100 py-4'
              >
                <Text className='text-text-100 font-semibold text-lg'>Continue Learning</Text>
              </Button>
            </MotiView>
          ) : (
            <MotiView
              from={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ type: 'spring', damping: 15, delay: 600 }}
              className='items-center gap-4'
            >
              <Button
                onPress={handleGetStarted}
                className='w-full bg-primary-100 py-4'
              >
                <Text className='text-text-100 font-semibold text-lg'>Get Started</Text>
              </Button>
              <Text className='text-text-200 text-sm text-center'>
                Join thousands of students preparing for their physiotherapy exams
              </Text>
            </MotiView>
          )}
        </View>

        {/* Social Proof */}
        <View className='px-6'>
          <MotiView
            from={{ opacity: 0, translateY: 30 }}
            animate={{ opacity: 1, translateY: 0 }}
            transition={{ type: 'spring', damping: 15, delay: 800 }}
          >
            <AnimatedCard
              title="Join Our Community"
              description="Connect with fellow students and share your progress"
              icon={<Users size={24} color="#00FFFF" />}
              variant="accent"
            >
              <View className='flex-row items-center mt-3 gap-4'>
                <View className='flex-row items-center'>
                  <Star size={16} color="#FFD700" fill="#FFD700" />
                  <Text className='text-text-200 text-sm ml-1'>4.9/5 Rating</Text>
                </View>
                <View className='flex-row items-center'>
                  <TrendingUp size={16} color="#00FF00" />
                  <Text className='text-text-200 text-sm ml-1'>95% Success Rate</Text>
                </View>
              </View>
            </AnimatedCard>
          </MotiView>
        </View>
      </ScrollView>

      {/* Floating Action Button */}
      <FloatingActionButton
        onPress={handleDailyQuestion}
        icon={<Play size={24} color="#FFFFFF" />}
        variant="primary"
        position="bottom-right"
      />

      {/* Bottom Navigation */}
      <BottomTabs />
    </View>
  );
}
