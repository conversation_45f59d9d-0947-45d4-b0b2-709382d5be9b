import * as React from 'react';
import { View, ScrollView } from 'react-native';
import { useRouter } from 'expo-router';
import { useRecoilValue } from 'recoil';
import { userState, isAuthenticatedState } from '~/lib/store/atoms';
import { useAuth } from '~/lib/hooks/useAuth';
import { Button } from '~/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '~/components/ui/card';
import { Text } from '~/components/ui/text';

export default function HomeScreen() {
  const router = useRouter();
  const user = useRecoilValue(userState);
  const isAuthenticated = useRecoilValue(isAuthenticatedState);
  const { authLoading } = useAuth();

  const handleGetStarted = () => {
    if (isAuthenticated) {
      router.push('/dashboard');
    } else {
      router.push('/auth');
    }
  };

  const handleDailyQuestion = () => {
    router.push('/daily-question');
  };

  if (authLoading) {
    return (
      <View className='flex-1 justify-center items-center bg-bg-100'>
        <Text className='text-text-100 text-lg'>Loading...</Text>
      </View>
    );
  }

  return (
    <ScrollView className='flex-1 bg-bg-100'>
      <View className='flex-1 justify-center items-center gap-8 p-6 min-h-screen'>
        {/* Hero Section */}
        <View className='items-center gap-4 max-w-md'>
          <Text className='text-4xl font-bold text-primary-100 text-center'>
            PhysioPrep
          </Text>
          <Text className='text-xl text-text-200 text-center'>
            Master Your Physiotherapy Exams
          </Text>
          <Text className='text-base text-text-200 text-center leading-6'>
            Comprehensive exam preparation with daily questions, practice quizzes,
            and full-length tests designed to help you succeed.
          </Text>
        </View>

        {/* Features Cards */}
        <View className='w-full max-w-md gap-4'>
          <Card className='bg-bg-200 border-bg-300'>
            <CardHeader>
              <CardTitle className='text-text-100'>Daily Questions</CardTitle>
              <CardDescription className='text-text-200'>
                Challenge yourself with a new question every day
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button
                onPress={handleDailyQuestion}
                className='w-full bg-accent-100'
              >
                <Text className='text-bg-100 font-semibold'>Try Today's Question</Text>
              </Button>
            </CardContent>
          </Card>

          <Card className='bg-bg-200 border-bg-300'>
            <CardHeader>
              <CardTitle className='text-text-100'>Practice Quizzes</CardTitle>
              <CardDescription className='text-text-200'>
                Quick quizzes to test your knowledge on specific topics
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className='bg-bg-200 border-bg-300'>
            <CardHeader>
              <CardTitle className='text-text-100'>Full Tests</CardTitle>
              <CardDescription className='text-text-200'>
                Comprehensive exams with detailed analytics and performance tracking
              </CardDescription>
            </CardHeader>
          </Card>
        </View>

        {/* CTA Section */}
        <View className='w-full max-w-md gap-4'>
          {isAuthenticated ? (
            <View className='items-center gap-4'>
              <Text className='text-text-100 text-center'>
                Welcome back, {user?.name}!
              </Text>
              <Button
                onPress={handleGetStarted}
                className='w-full bg-primary-100'
              >
                <Text className='text-text-100 font-semibold'>Continue Learning</Text>
              </Button>
            </View>
          ) : (
            <View className='items-center gap-4'>
              <Button
                onPress={handleGetStarted}
                className='w-full bg-primary-100'
              >
                <Text className='text-text-100 font-semibold'>Get Started</Text>
              </Button>
              <Text className='text-text-200 text-sm text-center'>
                Join thousands of students preparing for their physiotherapy exams
              </Text>
            </View>
          )}
        </View>

        {/* Stats Section */}
        <View className='w-full max-w-md'>
          <Card className='bg-bg-200 border-bg-300'>
            <CardContent className='pt-6'>
              <View className='flex-row justify-around'>
                <View className='items-center'>
                  <Text className='text-2xl font-bold text-primary-100'>1000+</Text>
                  <Text className='text-text-200 text-sm'>Questions</Text>
                </View>
                <View className='items-center'>
                  <Text className='text-2xl font-bold text-accent-100'>50+</Text>
                  <Text className='text-text-200 text-sm'>Topics</Text>
                </View>
                <View className='items-center'>
                  <Text className='text-2xl font-bold text-primary-100'>95%</Text>
                  <Text className='text-text-200 text-sm'>Pass Rate</Text>
                </View>
              </View>
            </CardContent>
          </Card>
        </View>
      </View>
    </ScrollView>
  );
}
