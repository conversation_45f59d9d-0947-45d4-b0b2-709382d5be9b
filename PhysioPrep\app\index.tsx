import * as React from 'react';
import { View } from 'react-native';
import { Redirect } from 'expo-router';
import { useRecoilValue } from 'recoil';
import { MotiView } from 'moti';
import { isAuthenticatedState, authLoadingState } from '~/lib/store/atoms';
import { Text } from '~/components/ui/text';

export default function IndexPage() {
  const isAuthenticated = useRecoilValue(isAuthenticatedState);
  const authLoading = useRecoilValue(authLoadingState);

  // Show loading screen while checking authentication
  if (authLoading) {
    return (
      <View className='flex-1 justify-center items-center bg-bg-100'>
        <MotiView
          from={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ type: 'spring', damping: 15 }}
        >
          <Text className='text-text-100 text-lg'>Loading...</Text>
        </MotiView>
      </View>
    );
  }

  // Redirect based on authentication status
  if (isAuthenticated) {
    return <Redirect href="/(tabs)/home" />;
  }

  return <Redirect href="/(auth)/welcome" />;
}
