import * as React from 'react';
import { useRouter } from 'expo-router';
import { useRecoilState } from 'recoil';
import { errorState } from '~/lib/store/atoms';

interface NavigationProviderProps {
  children: React.ReactNode;
}

export function NavigationProvider({ children }: NavigationProviderProps) {
  const router = useRouter();
  const [error, setError] = useRecoilState(errorState);

  // Global navigation error handler
  React.useEffect(() => {
    const handleNavigationError = (error: any) => {
      console.error('Navigation error:', error);
      setError({
        message: 'Navigation failed. Please try again.',
        code: 'NAVIGATION_ERROR',
        timestamp: new Date().toISOString(),
      });
    };

    // You can add global navigation listeners here if needed
    return () => {
      // Cleanup
    };
  }, []);

  return <>{children}</>;
}
