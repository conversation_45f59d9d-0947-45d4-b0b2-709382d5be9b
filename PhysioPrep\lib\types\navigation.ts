// Navigation route types for Expo Router
export type RootStackParamList = {
  // Root level
  '/': undefined;
  
  // Authentication routes
  '/(auth)/welcome': undefined;
  '/(auth)/login': undefined;
  '/(auth)/register': undefined;
  '/(auth)/forgot-password': undefined;
  '/(auth)/verify-email': { email?: string };
  
  // Main tab routes
  '/(tabs)/home': undefined;
  '/(tabs)/daily-question': undefined;
  '/(tabs)/quiz': undefined;
  '/(tabs)/test': undefined;
  '/(tabs)/profile': undefined;
  
  // Quiz flow routes
  '/(quiz)/index': undefined;
  '/(quiz)/[subject]': { subject: string };
  '/(quiz)/active/[quizId]': { quizId: string };
  '/(quiz)/results/[quizId]': { quizId: string };
  
  // Test flow routes
  '/(test)/index': undefined;
  '/(test)/[testId]': { testId: string };
  '/(test)/active/[testId]': { testId: string };
  '/(test)/results/[testId]': { testId: string };
  
  // Modal routes
  '/(modal)/edit-profile': undefined;
  '/(modal)/change-password': undefined;
  '/(modal)/notifications-settings': undefined;
  '/(modal)/premium': undefined;
  '/(modal)/help': undefined;
  '/(modal)/privacy': undefined;
  '/(modal)/terms': undefined;
  
  // Onboarding routes
  '/(onboarding)/welcome': undefined;
  '/(onboarding)/features': undefined;
  '/(onboarding)/preferences': undefined;
  '/(onboarding)/complete': undefined;
  
  // Legacy routes (for backward compatibility)
  '/auth': undefined;
  '/dashboard': undefined;
  '/daily-question': undefined;
  '/quiz': undefined;
  '/test': undefined;
  '/profile': undefined;
};

// Tab navigation types
export type TabParamList = {
  home: undefined;
  'daily-question': undefined;
  quiz: undefined;
  test: undefined;
  profile: undefined;
};

// Auth navigation types
export type AuthParamList = {
  welcome: undefined;
  login: undefined;
  register: undefined;
  'forgot-password': undefined;
  'verify-email': { email?: string };
};

// Quiz flow types
export type QuizParamList = {
  index: undefined;
  '[subject]': { subject: string };
  'active/[quizId]': { quizId: string };
  'results/[quizId]': { quizId: string };
};

// Test flow types
export type TestParamList = {
  index: undefined;
  '[testId]': { testId: string };
  'active/[testId]': { testId: string };
  'results/[testId]': { testId: string };
};

// Modal types
export type ModalParamList = {
  'edit-profile': undefined;
  'change-password': undefined;
  'notifications-settings': undefined;
  premium: undefined;
  help: undefined;
  privacy: undefined;
  terms: undefined;
};

// Onboarding types
export type OnboardingParamList = {
  welcome: undefined;
  features: undefined;
  preferences: undefined;
  complete: undefined;
};

// Navigation state types
export interface NavigationState {
  currentRoute: string;
  previousRoute?: string;
  isAuthenticated: boolean;
  isOnboarded: boolean;
  canGoBack: boolean;
}

// Route protection types
export type ProtectedRoute = keyof Pick<RootStackParamList, 
  | '/(tabs)/home'
  | '/(tabs)/daily-question'
  | '/(tabs)/quiz'
  | '/(tabs)/test'
  | '/(tabs)/profile'
  | '/(quiz)/index'
  | '/(quiz)/[subject]'
  | '/(quiz)/active/[quizId]'
  | '/(quiz)/results/[quizId]'
  | '/(test)/index'
  | '/(test)/[testId]'
  | '/(test)/active/[testId]'
  | '/(test)/results/[testId]'
  | '/(modal)/edit-profile'
  | '/(modal)/change-password'
  | '/(modal)/notifications-settings'
  | '/(modal)/premium'
  | '/(modal)/help'
  | '/(modal)/privacy'
  | '/(modal)/terms'
>;

export type PublicRoute = keyof Pick<RootStackParamList,
  | '/'
  | '/(auth)/welcome'
  | '/(auth)/login'
  | '/(auth)/register'
  | '/(auth)/forgot-password'
  | '/(auth)/verify-email'
  | '/(onboarding)/welcome'
  | '/(onboarding)/features'
  | '/(onboarding)/preferences'
  | '/(onboarding)/complete'
>;

// Deep linking types
export interface DeepLinkConfig {
  screens: {
    [K in keyof RootStackParamList]: string;
  };
}

// Navigation context types
export interface NavigationContextType {
  isReady: boolean;
  currentRoute: string;
  navigate: (route: keyof RootStackParamList, params?: any) => void;
  goBack: () => void;
  canGoBack: () => boolean;
  reset: (route: keyof RootStackParamList) => void;
}

// Route metadata
export interface RouteMetadata {
  title: string;
  description?: string;
  requiresAuth: boolean;
  requiresOnboarding: boolean;
  showTabBar: boolean;
  showHeader: boolean;
  gestureEnabled: boolean;
  animation?: 'slide_from_right' | 'slide_from_left' | 'slide_from_bottom' | 'fade' | 'none';
}

export const ROUTE_METADATA: Record<string, RouteMetadata> = {
  '/': {
    title: 'PhysioPrep',
    requiresAuth: false,
    requiresOnboarding: false,
    showTabBar: false,
    showHeader: false,
    gestureEnabled: true,
  },
  '/(auth)/welcome': {
    title: 'Welcome',
    requiresAuth: false,
    requiresOnboarding: false,
    showTabBar: false,
    showHeader: false,
    gestureEnabled: true,
  },
  '/(auth)/login': {
    title: 'Sign In',
    requiresAuth: false,
    requiresOnboarding: false,
    showTabBar: false,
    showHeader: false,
    gestureEnabled: true,
  },
  '/(tabs)/home': {
    title: 'Home',
    requiresAuth: true,
    requiresOnboarding: true,
    showTabBar: true,
    showHeader: false,
    gestureEnabled: true,
  },
  '/(quiz)/active/[quizId]': {
    title: 'Quiz in Progress',
    requiresAuth: true,
    requiresOnboarding: true,
    showTabBar: false,
    showHeader: false,
    gestureEnabled: false, // Prevent accidental navigation during quiz
  },
  '/(test)/active/[testId]': {
    title: 'Test in Progress',
    requiresAuth: true,
    requiresOnboarding: true,
    showTabBar: false,
    showHeader: false,
    gestureEnabled: false, // Prevent accidental navigation during test
  },
};
