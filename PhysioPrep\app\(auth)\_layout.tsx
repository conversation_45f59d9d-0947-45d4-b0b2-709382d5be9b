import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';

export default function AuthLayout() {
  return (
    <>
      <StatusBar style="light" backgroundColor="#0F0F0F" />
      <Stack
        screenOptions={{
          headerShown: false,
          contentStyle: { backgroundColor: '#0F0F0F' },
          animation: 'slide_from_right',
        }}
      >
        <Stack.Screen 
          name="welcome" 
          options={{
            title: 'Welcome',
          }}
        />
        <Stack.Screen 
          name="login" 
          options={{
            title: 'Sign In',
          }}
        />
        <Stack.Screen 
          name="register" 
          options={{
            title: 'Sign Up',
          }}
        />
        <Stack.Screen 
          name="forgot-password" 
          options={{
            title: 'Reset Password',
          }}
        />
        <Stack.Screen 
          name="verify-email" 
          options={{
            title: 'Verify Email',
          }}
        />
      </Stack>
    </>
  );
}
