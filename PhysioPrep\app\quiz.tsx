import * as React from 'react';
import { View, ScrollView, StatusBar, Pressable } from 'react-native';
import { useRouter } from 'expo-router';
import { <PERSON><PERSON><PERSON>ie<PERSON> } from 'moti';
import {
  ArrowLeft,
  Clock,
  BookOpen,
  Target,
  Filter,
  Play,
  Settings,
  Trophy,
  BarChart3,
  ChevronRight,
  Shuffle,
  Timer,
  CheckCircle
} from 'lucide-react-native';
import { Text } from '~/components/ui/text';
import { Button } from '~/components/ui/button';
import { AnimatedCard, StatsCard } from '~/components/ui/animated-card';
import { BottomTabs } from '~/components/navigation/bottom-tabs';
import { GridLayout, ResponsiveGrid } from '~/components/ui/grid-layout';
import { Collapsible } from '~/components/ui/collapsible';

// Mock data for demonstration
const mockSubjects = [
  {
    id: '1',
    name: 'Anatomy',
    description: 'Human body structure and systems',
    color: '#FF6B6B',
    questionCount: 245,
    completedQuizzes: 12,
    averageScore: 85,
    icon: '🦴',
  },
  {
    id: '2',
    name: 'Physiology',
    description: 'Body functions and processes',
    color: '#00FFFF',
    questionCount: 198,
    completedQuizzes: 8,
    averageScore: 78,
    icon: '❤️',
  },
  {
    id: '3',
    name: 'Pathology',
    description: 'Disease processes and conditions',
    color: '#FFD700',
    questionCount: 167,
    completedQuizzes: 5,
    averageScore: 72,
    icon: '🔬',
  },
  {
    id: '4',
    name: 'Biomechanics',
    description: 'Movement and mechanical principles',
    color: '#10B981',
    questionCount: 134,
    completedQuizzes: 7,
    averageScore: 81,
    icon: '⚙️',
  },
  {
    id: '5',
    name: 'Exercise Therapy',
    description: 'Therapeutic exercises and rehabilitation',
    color: '#8B5CF6',
    questionCount: 189,
    completedQuizzes: 9,
    averageScore: 88,
    icon: '🏃‍♂️',
  },
  {
    id: '6',
    name: 'Manual Therapy',
    description: 'Hands-on treatment techniques',
    color: '#F59E0B',
    questionCount: 156,
    completedQuizzes: 6,
    averageScore: 76,
    icon: '🤲',
  },
];

const mockRecentQuizzes = [
  {
    id: '1',
    subject: 'Anatomy',
    title: 'Musculoskeletal System',
    score: 85,
    totalQuestions: 20,
    completedAt: '2 hours ago',
    duration: '12:34',
  },
  {
    id: '2',
    subject: 'Physiology',
    title: 'Cardiovascular System',
    score: 92,
    totalQuestions: 15,
    completedAt: 'Yesterday',
    duration: '8:45',
  },
  {
    id: '3',
    subject: 'Pathology',
    title: 'Inflammatory Conditions',
    score: 78,
    totalQuestions: 25,
    completedAt: '2 days ago',
    duration: '18:22',
  },
];

type QuizMode = 'selection' | 'setup' | 'active';

export default function QuizScreen() {
  const router = useRouter();
  const [mode, setMode] = React.useState<QuizMode>('selection');
  const [selectedSubject, setSelectedSubject] = React.useState<any>(null);
  const [quizSettings, setQuizSettings] = React.useState({
    questionCount: 10,
    difficulty: 'mixed',
    timeLimit: 0, // 0 means no time limit
    shuffleQuestions: true,
    showCorrectAnswers: true,
  });

  const handleSubjectSelect = (subject: any) => {
    setSelectedSubject(subject);
    setMode('setup');
  };

  const handleStartQuiz = () => {
    setMode('active');
    // Here you would start the actual quiz
  };

  const handleBackToSelection = () => {
    setMode('selection');
    setSelectedSubject(null);
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return '#10B981';
      case 'medium': return '#F59E0B';
      case 'hard': return '#EF4444';
      default: return '#6B7280';
    }
  };

  const renderSubjectSelection = () => (
    <ScrollView
      className='flex-1'
      showsVerticalScrollIndicator={false}
      contentContainerStyle={{ paddingBottom: 100 }}
    >
      {/* Header */}
      <MotiView
        from={{ opacity: 0, translateY: -30 }}
        animate={{ opacity: 1, translateY: 0 }}
        transition={{ type: 'spring', damping: 15 }}
        className='flex-row items-center justify-between px-6 pt-16 pb-6'
      >
        <Button
          onPress={() => router.back()}
          variant="ghost"
          size="sm"
          className='p-2'
        >
          <ArrowLeft size={24} color="#e0e0e0" />
        </Button>

        <Text className='text-text-100 text-xl font-bold'>Practice Quiz</Text>

        <Button
          onPress={() => {/* Filter functionality */}}
          variant="ghost"
          size="sm"
          className='p-2'
        >
          <Filter size={24} color="#e0e0e0" />
        </Button>
      </MotiView>

      {/* Quick Stats */}
      <View className='px-6 mb-6'>
        <GridLayout columns={3} spacing={12} animated={true}>
          <StatsCard
            title="Total Quizzes"
            value="47"
            icon={<BookOpen size={16} color="#FF6B6B" />}
          />
          <StatsCard
            title="Avg Score"
            value="82%"
            icon={<Target size={16} color="#00FFFF" />}
          />
          <StatsCard
            title="Best Streak"
            value="12"
            icon={<Trophy size={16} color="#FFD700" />}
          />
        </GridLayout>
      </View>

      {/* Subject Selection */}
      <View className='px-6 mb-6'>
        <Text className='text-text-100 text-lg font-semibold mb-4'>
          Choose Your Subject
        </Text>

        <ResponsiveGrid
          minItemWidth={160}
          maxColumns={2}
          spacing={12}
          animated={true}
        >
          {mockSubjects.map((subject, index) => (
            <MotiView
              key={subject.id}
              from={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{
                type: 'spring',
                damping: 15,
                delay: index * 100
              }}
            >
              <Pressable
                onPress={() => handleSubjectSelect(subject)}
                className='bg-bg-200 border border-bg-300 rounded-xl p-4 active:opacity-80'
              >
                <View className='items-center mb-3'>
                  <Text className='text-3xl mb-2'>{subject.icon}</Text>
                  <Text className='text-text-100 font-semibold text-center'>
                    {subject.name}
                  </Text>
                  <Text className='text-text-200 text-xs text-center mt-1'>
                    {subject.description}
                  </Text>
                </View>

                <View className='gap-2'>
                  <View className='flex-row justify-between'>
                    <Text className='text-text-200 text-xs'>Questions</Text>
                    <Text className='text-text-100 text-xs font-semibold'>
                      {subject.questionCount}
                    </Text>
                  </View>
                  <View className='flex-row justify-between'>
                    <Text className='text-text-200 text-xs'>Avg Score</Text>
                    <Text
                      className='text-xs font-semibold'
                      style={{ color: subject.color }}
                    >
                      {subject.averageScore}%
                    </Text>
                  </View>
                  <View className='flex-row justify-between'>
                    <Text className='text-text-200 text-xs'>Completed</Text>
                    <Text className='text-text-100 text-xs font-semibold'>
                      {subject.completedQuizzes}
                    </Text>
                  </View>
                </View>
              </Pressable>
            </MotiView>
          ))}
        </ResponsiveGrid>
      </View>

      {/* Recent Quizzes */}
      <View className='px-6 mb-6'>
        <Collapsible
          title="Recent Quizzes"
          icon={<Clock size={20} color="#e0e0e0" />}
          defaultExpanded={false}
        >
          <View className='gap-3'>
            {mockRecentQuizzes.map((quiz, index) => (
              <MotiView
                key={quiz.id}
                from={{ opacity: 0, translateX: -20 }}
                animate={{ opacity: 1, translateX: 0 }}
                transition={{
                  type: 'spring',
                  damping: 15,
                  delay: index * 100
                }}
              >
                <View className='bg-bg-300/50 rounded-lg p-3'>
                  <View className='flex-row items-center justify-between mb-2'>
                    <Text className='text-text-100 font-semibold'>
                      {quiz.title}
                    </Text>
                    <Text
                      className='text-sm font-bold'
                      style={{
                        color: quiz.score >= 80 ? '#10B981' :
                               quiz.score >= 60 ? '#F59E0B' : '#EF4444'
                      }}
                    >
                      {quiz.score}%
                    </Text>
                  </View>
                  <View className='flex-row items-center justify-between'>
                    <Text className='text-text-200 text-xs'>
                      {quiz.subject} • {quiz.totalQuestions} questions
                    </Text>
                    <Text className='text-text-200 text-xs'>
                      {quiz.duration} • {quiz.completedAt}
                    </Text>
                  </View>
                </View>
              </MotiView>
            ))}
          </View>
        </Collapsible>
      </View>
    </ScrollView>
  );

  const renderQuizSetup = () => (
    <ScrollView
      className='flex-1'
      showsVerticalScrollIndicator={false}
      contentContainerStyle={{ paddingBottom: 100 }}
    >
      {/* Header */}
      <MotiView
        from={{ opacity: 0, translateY: -30 }}
        animate={{ opacity: 1, translateY: 0 }}
        transition={{ type: 'spring', damping: 15 }}
        className='flex-row items-center justify-between px-6 pt-16 pb-6'
      >
        <Button
          onPress={handleBackToSelection}
          variant="ghost"
          size="sm"
          className='p-2'
        >
          <ArrowLeft size={24} color="#e0e0e0" />
        </Button>

        <Text className='text-text-100 text-xl font-bold'>Quiz Setup</Text>

        <View className='w-10' />
      </MotiView>

      {/* Selected Subject */}
      <View className='px-6 mb-6'>
        <MotiView
          from={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ type: 'spring', damping: 15, delay: 200 }}
        >
          <AnimatedCard
            title={selectedSubject?.name || ''}
            description={selectedSubject?.description || ''}
            variant="primary"
            className='p-4'
          >
            <View className='flex-row items-center justify-between mt-3'>
              <Text className='text-text-200 text-sm'>
                {selectedSubject?.questionCount} questions available
              </Text>
              <Text className='text-3xl'>{selectedSubject?.icon}</Text>
            </View>
          </AnimatedCard>
        </MotiView>
      </View>

      {/* Quiz Settings */}
      <View className='px-6 mb-6'>
        <Text className='text-text-100 text-lg font-semibold mb-4'>
          Quiz Settings
        </Text>

        <View className='gap-4'>
          {/* Question Count */}
          <AnimatedCard
            title="Number of Questions"
            variant="default"
            className='p-4'
          >
            <View className='flex-row items-center justify-between mt-3'>
              <Text className='text-text-200'>Questions</Text>
              <View className='flex-row items-center gap-3'>
                <Button
                  onPress={() => setQuizSettings(prev => ({
                    ...prev,
                    questionCount: Math.max(5, prev.questionCount - 5)
                  }))}
                  variant="outline"
                  size="sm"
                  className='w-8 h-8 p-0'
                >
                  <Text className='text-text-100'>-</Text>
                </Button>
                <Text className='text-text-100 font-semibold min-w-[30px] text-center'>
                  {quizSettings.questionCount}
                </Text>
                <Button
                  onPress={() => setQuizSettings(prev => ({
                    ...prev,
                    questionCount: Math.min(50, prev.questionCount + 5)
                  }))}
                  variant="outline"
                  size="sm"
                  className='w-8 h-8 p-0'
                >
                  <Text className='text-text-100'>+</Text>
                </Button>
              </View>
            </View>
          </AnimatedCard>

          {/* Difficulty */}
          <AnimatedCard
            title="Difficulty Level"
            variant="default"
            className='p-4'
          >
            <View className='flex-row gap-2 mt-3'>
              {['easy', 'medium', 'hard', 'mixed'].map((difficulty) => (
                <Button
                  key={difficulty}
                  onPress={() => setQuizSettings(prev => ({ ...prev, difficulty }))}
                  variant={quizSettings.difficulty === difficulty ? "default" : "outline"}
                  size="sm"
                  className={`flex-1 ${
                    quizSettings.difficulty === difficulty
                      ? 'bg-primary-100'
                      : 'border-bg-300'
                  }`}
                >
                  <Text
                    className={`text-xs capitalize ${
                      quizSettings.difficulty === difficulty
                        ? 'text-text-100'
                        : 'text-text-200'
                    }`}
                  >
                    {difficulty}
                  </Text>
                </Button>
              ))}
            </View>
          </AnimatedCard>

          {/* Time Limit */}
          <AnimatedCard
            title="Time Limit"
            variant="default"
            className='p-4'
          >
            <View className='flex-row gap-2 mt-3'>
              {[0, 10, 15, 30].map((minutes) => (
                <Button
                  key={minutes}
                  onPress={() => setQuizSettings(prev => ({ ...prev, timeLimit: minutes }))}
                  variant={quizSettings.timeLimit === minutes ? "default" : "outline"}
                  size="sm"
                  className={`flex-1 ${
                    quizSettings.timeLimit === minutes
                      ? 'bg-accent-100'
                      : 'border-bg-300'
                  }`}
                >
                  <Text
                    className={`text-xs ${
                      quizSettings.timeLimit === minutes
                        ? 'text-bg-100'
                        : 'text-text-200'
                    }`}
                  >
                    {minutes === 0 ? 'No Limit' : `${minutes}m`}
                  </Text>
                </Button>
              ))}
            </View>
          </AnimatedCard>

          {/* Additional Options */}
          <AnimatedCard
            title="Options"
            variant="default"
            className='p-4'
          >
            <View className='gap-3 mt-3'>
              <View className='flex-row items-center justify-between'>
                <View className='flex-row items-center'>
                  <Shuffle size={16} color="#e0e0e0" />
                  <Text className='text-text-200 ml-2'>Shuffle Questions</Text>
                </View>
                <Button
                  onPress={() => setQuizSettings(prev => ({
                    ...prev,
                    shuffleQuestions: !prev.shuffleQuestions
                  }))}
                  variant="ghost"
                  size="sm"
                  className={`w-12 h-6 rounded-full ${
                    quizSettings.shuffleQuestions ? 'bg-primary-100' : 'bg-bg-300'
                  }`}
                >
                  <View
                    className={`w-4 h-4 rounded-full bg-white transition-transform ${
                      quizSettings.shuffleQuestions ? 'translate-x-3' : '-translate-x-3'
                    }`}
                  />
                </Button>
              </View>

              <View className='flex-row items-center justify-between'>
                <View className='flex-row items-center'>
                  <CheckCircle size={16} color="#e0e0e0" />
                  <Text className='text-text-200 ml-2'>Show Correct Answers</Text>
                </View>
                <Button
                  onPress={() => setQuizSettings(prev => ({
                    ...prev,
                    showCorrectAnswers: !prev.showCorrectAnswers
                  }))}
                  variant="ghost"
                  size="sm"
                  className={`w-12 h-6 rounded-full ${
                    quizSettings.showCorrectAnswers ? 'bg-primary-100' : 'bg-bg-300'
                  }`}
                >
                  <View
                    className={`w-4 h-4 rounded-full bg-white transition-transform ${
                      quizSettings.showCorrectAnswers ? 'translate-x-3' : '-translate-x-3'
                    }`}
                  />
                </Button>
              </View>
            </View>
          </AnimatedCard>
        </View>
      </View>

      {/* Start Quiz Button */}
      <View className='px-6 mb-6'>
        <MotiView
          from={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ type: 'spring', damping: 15, delay: 600 }}
        >
          <Button
            onPress={handleStartQuiz}
            className='w-full bg-primary-100 py-4'
          >
            <Play size={20} color="#FFFFFF" />
            <Text className='text-text-100 font-semibold text-lg ml-2'>
              Start Quiz
            </Text>
          </Button>
        </MotiView>
      </View>
    </ScrollView>
  );

  const renderActiveQuiz = () => (
    <View className='flex-1 justify-center items-center'>
      <MotiView
        from={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ type: 'spring', damping: 15 }}
        className='items-center'
      >
        <Play size={64} color="#FF6B6B" />
        <Text className='text-text-100 text-2xl font-bold mt-4'>
          Quiz Starting...
        </Text>
        <Text className='text-text-200 text-center mt-2'>
          Get ready for {quizSettings.questionCount} questions on {selectedSubject?.name}
        </Text>
      </MotiView>
    </View>
  );

  return (
    <View className='flex-1 bg-bg-100'>
      <StatusBar barStyle="light-content" backgroundColor="#0F0F0F" />

      {mode === 'selection' && renderSubjectSelection()}
      {mode === 'setup' && renderQuizSetup()}
      {mode === 'active' && renderActiveQuiz()}

      {/* Bottom Navigation */}
      <BottomTabs />
    </View>
  );
}
