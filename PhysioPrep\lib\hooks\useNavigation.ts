import { useRouter, useSegments } from 'expo-router';
import { useRecoilValue } from 'recoil';
import { isAuthenticatedState } from '~/lib/store/atoms';

export interface NavigationHelpers {
  // Authentication navigation
  goToLogin: () => void;
  goToRegister: () => void;
  goToForgotPassword: () => void;
  
  // Main app navigation
  goToHome: () => void;
  goToDailyQuestion: () => void;
  goToQuiz: (subjectId?: string) => void;
  goToTest: (testId?: string) => void;
  goToProfile: () => void;
  
  // Modal navigation
  goToEditProfile: () => void;
  goToChangePassword: () => void;
  goToNotificationSettings: () => void;
  goToPremium: () => void;
  goToHelp: () => void;
  goToPrivacy: () => void;
  goToTerms: () => void;
  
  // Quiz flow navigation
  startQuiz: (subjectId: string) => void;
  goToQuizResults: (quizId: string) => void;
  
  // Test flow navigation
  startTest: (testId: string) => void;
  goToTestResults: (testId: string) => void;
  
  // Onboarding navigation
  goToOnboarding: () => void;
  completeOnboarding: () => void;
  
  // Utility functions
  goBack: () => void;
  canGoBack: () => boolean;
  getCurrentRoute: () => string;
  isInAuthFlow: () => boolean;
  isInTabsFlow: () => boolean;
}

export function useNavigation(): NavigationHelpers {
  const router = useRouter();
  const segments = useSegments();
  const isAuthenticated = useRecoilValue(isAuthenticatedState);

  const getCurrentRoute = () => {
    return segments.join('/');
  };

  const isInAuthFlow = () => {
    return segments[0] === '(auth)';
  };

  const isInTabsFlow = () => {
    return segments[0] === '(tabs)';
  };

  const canGoBack = () => {
    return router.canGoBack();
  };

  const goBack = () => {
    if (router.canGoBack()) {
      router.back();
    }
  };

  // Authentication navigation
  const goToLogin = () => {
    router.push('/(auth)/login');
  };

  const goToRegister = () => {
    router.push('/(auth)/register');
  };

  const goToForgotPassword = () => {
    router.push('/(auth)/forgot-password');
  };

  // Main app navigation
  const goToHome = () => {
    if (isAuthenticated) {
      router.push('/(tabs)/home');
    } else {
      goToLogin();
    }
  };

  const goToDailyQuestion = () => {
    if (isAuthenticated) {
      router.push('/(tabs)/daily-question');
    } else {
      goToLogin();
    }
  };

  const goToQuiz = (subjectId?: string) => {
    if (isAuthenticated) {
      if (subjectId) {
        router.push(`/(quiz)/${subjectId}`);
      } else {
        router.push('/(tabs)/quiz');
      }
    } else {
      goToLogin();
    }
  };

  const goToTest = (testId?: string) => {
    if (isAuthenticated) {
      if (testId) {
        router.push(`/(test)/${testId}`);
      } else {
        router.push('/(tabs)/test');
      }
    } else {
      goToLogin();
    }
  };

  const goToProfile = () => {
    if (isAuthenticated) {
      router.push('/(tabs)/profile');
    } else {
      goToLogin();
    }
  };

  // Modal navigation
  const goToEditProfile = () => {
    router.push('/(modal)/edit-profile');
  };

  const goToChangePassword = () => {
    router.push('/(modal)/change-password');
  };

  const goToNotificationSettings = () => {
    router.push('/(modal)/notifications-settings');
  };

  const goToPremium = () => {
    router.push('/(modal)/premium');
  };

  const goToHelp = () => {
    router.push('/(modal)/help');
  };

  const goToPrivacy = () => {
    router.push('/(modal)/privacy');
  };

  const goToTerms = () => {
    router.push('/(modal)/terms');
  };

  // Quiz flow navigation
  const startQuiz = (subjectId: string) => {
    router.push(`/(quiz)/active/${subjectId}`);
  };

  const goToQuizResults = (quizId: string) => {
    router.push(`/(quiz)/results/${quizId}`);
  };

  // Test flow navigation
  const startTest = (testId: string) => {
    router.push(`/(test)/active/${testId}`);
  };

  const goToTestResults = (testId: string) => {
    router.push(`/(test)/results/${testId}`);
  };

  // Onboarding navigation
  const goToOnboarding = () => {
    router.push('/(onboarding)/welcome');
  };

  const completeOnboarding = () => {
    router.replace('/(tabs)/home');
  };

  return {
    // Authentication navigation
    goToLogin,
    goToRegister,
    goToForgotPassword,
    
    // Main app navigation
    goToHome,
    goToDailyQuestion,
    goToQuiz,
    goToTest,
    goToProfile,
    
    // Modal navigation
    goToEditProfile,
    goToChangePassword,
    goToNotificationSettings,
    goToPremium,
    goToHelp,
    goToPrivacy,
    goToTerms,
    
    // Quiz flow navigation
    startQuiz,
    goToQuizResults,
    
    // Test flow navigation
    startTest,
    goToTestResults,
    
    // Onboarding navigation
    goToOnboarding,
    completeOnboarding,
    
    // Utility functions
    goBack,
    canGoBack,
    getCurrentRoute,
    isInAuthFlow,
    isInTabsFlow,
  };
}
