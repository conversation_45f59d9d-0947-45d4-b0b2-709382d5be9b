import * as React from 'react';
import { View, ScrollView } from 'react-native';
import { useRouter } from 'expo-router';
import { MotiView } from 'moti';
import {
  BookOpen,
  FileText,
  Trophy,
  Users,
  TrendingUp,
  Star,
  Play
} from 'lucide-react-native';
import { Button } from '~/components/ui/button';
import { Text } from '~/components/ui/text';
import { AnimatedCard, StatsCard } from '~/components/ui/animated-card';
import { FloatingActionButton } from '~/components/ui/floating-action-button';
import { GridLayout } from '~/components/ui/grid-layout';

export default function WelcomeScreen() {
  const router = useRouter();

  const handleGetStarted = () => {
    router.push('/(auth)/register');
  };

  const handleSignIn = () => {
    router.push('/(auth)/login');
  };

  const handleDailyQuestion = () => {
    router.push('/(auth)/register');
  };

  return (
    <View className='flex-1 bg-bg-100'>
      <ScrollView 
        className='flex-1'
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 100 }}
      >
        {/* Hero Section */}
        <MotiView
          from={{ opacity: 0, translateY: -50 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ type: 'spring', damping: 15, delay: 200 }}
          className='items-center px-6 pt-16 pb-8'
        >
          <Text className='text-5xl font-bold text-primary-100 text-center mb-2'>
            PhysioPrep
          </Text>
          <Text className='text-xl text-text-200 text-center mb-4'>
            Master Your Physiotherapy Exams
          </Text>
          <Text className='text-base text-text-200 text-center leading-6 max-w-sm'>
            Comprehensive exam preparation with daily questions, practice quizzes, 
            and full-length tests designed to help you succeed.
          </Text>
        </MotiView>

        {/* Quick Stats */}
        <View className='px-6 mb-8'>
          <GridLayout columns={3} spacing={12} animated={true}>
            <StatsCard
              title="Questions"
              value="1000+"
              icon={<BookOpen size={20} color="#FF6B6B" />}
            />
            <StatsCard
              title="Topics"
              value="50+"
              icon={<FileText size={20} color="#00FFFF" />}
            />
            <StatsCard
              title="Pass Rate"
              value="95%"
              icon={<Trophy size={20} color="#FF6B6B" />}
            />
          </GridLayout>
        </View>

        {/* Feature Cards */}
        <View className='px-6 mb-8'>
          <Text className='text-text-100 text-xl font-semibold mb-4'>
            What You'll Get
          </Text>
          
          <View className='gap-4'>
            <AnimatedCard
              title="Daily Questions"
              description="Challenge yourself with curated questions every day"
              icon={<Play size={24} color="#00FFFF" />}
              variant="accent"
            />
            
            <AnimatedCard
              title="Practice Quizzes"
              description="Quick quizzes on specific topics to reinforce learning"
              icon={<BookOpen size={24} color="#FF6B6B" />}
              variant="primary"
            />
            
            <AnimatedCard
              title="Full-Length Tests"
              description="Comprehensive exam simulations with detailed analytics"
              icon={<FileText size={24} color="#e0e0e0" />}
              variant="default"
            />
          </View>
        </View>

        {/* CTA Section */}
        <View className='px-6 mb-8'>
          <MotiView
            from={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ type: 'spring', damping: 15, delay: 600 }}
            className='gap-4'
          >
            <Button 
              onPress={handleGetStarted}
              className='w-full bg-primary-100 py-4'
            >
              <Text className='text-text-100 font-semibold text-lg'>Get Started Free</Text>
            </Button>
            
            <Button 
              onPress={handleSignIn}
              variant="outline"
              className='w-full py-4 border-bg-300'
            >
              <Text className='text-text-200 font-semibold text-lg'>Already have an account? Sign In</Text>
            </Button>
          </MotiView>
        </View>

        {/* Social Proof */}
        <View className='px-6'>
          <MotiView
            from={{ opacity: 0, translateY: 30 }}
            animate={{ opacity: 1, translateY: 0 }}
            transition={{ type: 'spring', damping: 15, delay: 800 }}
          >
            <AnimatedCard
              title="Join Our Community"
              description="Connect with fellow students and share your progress"
              icon={<Users size={24} color="#00FFFF" />}
              variant="accent"
            >
              <View className='flex-row items-center mt-3 gap-4'>
                <View className='flex-row items-center'>
                  <Star size={16} color="#FFD700" fill="#FFD700" />
                  <Text className='text-text-200 text-sm ml-1'>4.9/5 Rating</Text>
                </View>
                <View className='flex-row items-center'>
                  <TrendingUp size={16} color="#00FF00" />
                  <Text className='text-text-200 text-sm ml-1'>95% Success Rate</Text>
                </View>
              </View>
            </AnimatedCard>
          </MotiView>
        </View>
      </ScrollView>

      {/* Floating Action Button */}
      <FloatingActionButton
        onPress={handleDailyQuestion}
        icon={<Play size={24} color="#FFFFFF" />}
        variant="primary"
        position="bottom-right"
      />
    </View>
  );
}
