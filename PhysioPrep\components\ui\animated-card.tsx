import * as React from 'react';
import { Pressable, View } from 'react-native';
import { MotiView } from 'moti';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './card';
import { Text } from './text';
import { cn } from '~/lib/utils';

interface AnimatedCardProps {
  title: string;
  description?: string;
  icon?: React.ReactNode;
  onPress?: () => void;
  className?: string;
  children?: React.ReactNode;
  variant?: 'default' | 'primary' | 'accent' | 'success' | 'warning' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
}

const variantStyles = {
  default: 'bg-bg-200 border-bg-300',
  primary: 'bg-primary-100/10 border-primary-100/30',
  accent: 'bg-accent-100/10 border-accent-100/30',
  success: 'bg-green-500/10 border-green-500/30',
  warning: 'bg-yellow-500/10 border-yellow-500/30',
  danger: 'bg-red-500/10 border-red-500/30',
};

const sizeStyles = {
  sm: 'p-3',
  md: 'p-4',
  lg: 'p-6',
};

export function AnimatedCard({
  title,
  description,
  icon,
  onPress,
  className,
  children,
  variant = 'default',
  size = 'md',
  disabled = false,
}: AnimatedCardProps) {
  const [isPressed, setIsPressed] = React.useState(false);

  const handlePressIn = () => {
    if (!disabled) setIsPressed(true);
  };

  const handlePressOut = () => {
    setIsPressed(false);
  };

  const CardComponent = onPress ? Pressable : View;

  return (
    <MotiView
      animate={{
        scale: isPressed ? 0.98 : 1,
        opacity: disabled ? 0.6 : 1,
      }}
      transition={{
        type: 'timing',
        duration: 150,
      }}
    >
      <CardComponent
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        disabled={disabled}
        className={cn(
          'rounded-xl border',
          variantStyles[variant],
          sizeStyles[size],
          onPress && !disabled && 'active:opacity-80',
          disabled && 'opacity-60',
          className
        )}
      >
        <View className="flex-row items-start space-x-3">
          {icon && (
            <View className="mt-1">
              {icon}
            </View>
          )}
          <View className="flex-1">
            <Text className="text-text-100 font-semibold text-base mb-1">
              {title}
            </Text>
            {description && (
              <Text className="text-text-200 text-sm leading-5">
                {description}
              </Text>
            )}
            {children}
          </View>
        </View>
      </CardComponent>
    </MotiView>
  );
}

interface StatsCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon?: React.ReactNode;
  trend?: 'up' | 'down' | 'neutral';
  trendValue?: string;
  className?: string;
}

export function StatsCard({
  title,
  value,
  subtitle,
  icon,
  trend,
  trendValue,
  className,
}: StatsCardProps) {
  const trendColors = {
    up: 'text-green-400',
    down: 'text-red-400',
    neutral: 'text-text-200',
  };

  return (
    <MotiView
      from={{ opacity: 0, translateY: 20 }}
      animate={{ opacity: 1, translateY: 0 }}
      transition={{ type: 'timing', duration: 300 }}
    >
      <Card className={cn('bg-bg-200 border-bg-300', className)}>
        <CardContent className="p-4">
          <View className="flex-row items-center justify-between mb-2">
            <Text className="text-text-200 text-sm font-medium">
              {title}
            </Text>
            {icon}
          </View>
          <View className="flex-row items-end space-x-2">
            <Text className="text-text-100 text-2xl font-bold">
              {value}
            </Text>
            {trend && trendValue && (
              <Text className={cn('text-xs font-medium', trendColors[trend])}>
                {trendValue}
              </Text>
            )}
          </View>
          {subtitle && (
            <Text className="text-text-200 text-xs mt-1">
              {subtitle}
            </Text>
          )}
        </CardContent>
      </Card>
    </MotiView>
  );
}

interface ProgressCardProps {
  title: string;
  progress: number;
  total?: number;
  description?: string;
  color?: string;
  className?: string;
}

export function ProgressCard({
  title,
  progress,
  total,
  description,
  color = '#FF6B6B',
  className,
}: ProgressCardProps) {
  const percentage = total ? (progress / total) * 100 : progress;

  return (
    <MotiView
      from={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ type: 'spring', damping: 15 }}
    >
      <Card className={cn('bg-bg-200 border-bg-300', className)}>
        <CardContent className="p-4">
          <View className="flex-row items-center justify-between mb-3">
            <Text className="text-text-100 font-semibold">
              {title}
            </Text>
            <Text className="text-text-200 text-sm">
              {total ? `${progress}/${total}` : `${Math.round(percentage)}%`}
            </Text>
          </View>
          
          <View className="bg-bg-300 rounded-full h-2 mb-2">
            <MotiView
              from={{ width: 0 }}
              animate={{ width: `${percentage}%` }}
              transition={{ type: 'timing', duration: 800, delay: 200 }}
              style={{ backgroundColor: color }}
              className="h-full rounded-full"
            />
          </View>
          
          {description && (
            <Text className="text-text-200 text-xs">
              {description}
            </Text>
          )}
        </CardContent>
      </Card>
    </MotiView>
  );
}
