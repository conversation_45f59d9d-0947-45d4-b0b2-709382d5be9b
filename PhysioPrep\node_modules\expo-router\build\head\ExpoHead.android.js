"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Head = Head;
const react_1 = __importDefault(require("react"));
function Head(props) {
    return null;
}
Head.Provider = react_1.default.Fragment;
//# sourceMappingURL=ExpoHead.android.js.map