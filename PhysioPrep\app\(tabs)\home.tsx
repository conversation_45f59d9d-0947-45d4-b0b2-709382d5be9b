import * as React from "react";
import { View, ScrollView, RefreshControl } from "react-native";
import { useRouter } from "expo-router";
import { useRecoilValue } from "recoil";
import { <PERSON><PERSON>View } from "moti";
import {
  Calendar,
  BookOpen,
  FileText,
  Trophy,
  Clock,
  Target,
  Award,
  Settings,
  Bell,
  ChevronRight,
  Play,
  BarChart3,
} from "lucide-react-native";
import { userState, isAuthenticatedState } from "~/lib/store/atoms";
import { Text } from "~/components/ui/text";
import { Button } from "~/components/ui/button";
import {
  AnimatedCard,
  StatsCard,
  ProgressCard,
} from "~/components/ui/animated-card";
import { ExtendedFloatingActionButton } from "~/components/ui/floating-action-button";
import { GridLayout } from "~/components/ui/grid-layout";
import { Collapsible } from "~/components/ui/collapsible";

export default function HomeScreen() {
  const router = useRouter();
  const user = useRecoilValue(userState);
  const isAuthenticated = useRecoilValue(isAuthenticatedState);

  const [refreshing, setRefreshing] = React.useState(false);
  const [currentTime, setCurrentTime] = React.useState(new Date());

  React.useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000); // Update every minute

    return () => clearInterval(timer);
  }, []);

  React.useEffect(() => {
    if (!isAuthenticated) {
      router.replace("/login");
    }
  }, [isAuthenticated]);

  const onRefresh = React.useCallback(async () => {
    setRefreshing(true);
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000));
    setRefreshing(false);
  }, []);

  const getGreeting = () => {
    const hour = currentTime.getHours();
    if (hour < 12) return "Good Morning";
    if (hour < 17) return "Good Afternoon";
    return "Good Evening";
  };

  const getStreakEmoji = (streak: number) => {
    if (streak >= 30) return "🔥";
    if (streak >= 14) return "⚡";
    if (streak >= 7) return "💪";
    if (streak >= 3) return "🌟";
    return "👍";
  };

  if (!user) {
    return (
      <View className="flex-1 justify-center items-center bg-bg-100">
        <Text className="text-text-100 text-lg">Loading...</Text>
      </View>
    );
  }

  return (
    <View className="flex-1 bg-bg-100">
      <ScrollView
        className="flex-1"
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 100 }}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor="#FF6B6B"
            colors={["#FF6B6B"]}
          />
        }
      >
        {/* Header */}
        <MotiView
          from={{ opacity: 0, translateY: -30 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ type: "spring", damping: 15 }}
          className="flex-row items-center justify-between px-6 pt-16 pb-6"
        >
          <View className="flex-1">
            <Text className="text-text-200 text-sm">
              {getGreeting()}, {user.name}! 👋
            </Text>
            <Text className="text-text-100 text-2xl font-bold mt-1">
              Ready to Study?
            </Text>
          </View>

          <View className="flex-row items-center gap-3">
            <Button
              // onPress={() => router.push("/notifications")}
              variant="ghost"
              size="sm"
              className="p-2"
            >
              <Bell size={24} color="#e0e0e0" />
            </Button>
            <Button
              onPress={() => router.push("/(tabs)/profile")}
              variant="ghost"
              size="sm"
              className="p-2"
            >
              <Settings size={24} color="#e0e0e0" />
            </Button>
          </View>
        </MotiView>

        {/* Current Streak */}
        <View className="px-6 mb-6">
          <MotiView
            from={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ type: "spring", damping: 15, delay: 200 }}
          >
            <AnimatedCard
              title={`${user.stats.currentStreak} Day Streak ${getStreakEmoji(
                user.stats.currentStreak
              )}`}
              description="Keep it up! Consistency is key to success."
              variant="primary"
              className="bg-gradient-to-r from-primary-100/20 to-accent-100/20"
            >
              <View className="flex-row items-center justify-between mt-4">
                <View className="flex-row items-center">
                  <Trophy size={20} color="#FF6B6B" />
                  <Text className="text-text-200 text-sm ml-2">
                    Longest: {user.stats.longestStreak} days
                  </Text>
                </View>
                <Button
                  onPress={() => router.push("/(tabs)/daily-question")}
                  variant="ghost"
                  size="sm"
                  className="bg-primary-100/20 px-4 py-2"
                >
                  <Text className="text-primary-100 font-semibold">
                    Continue
                  </Text>
                </Button>
              </View>
            </AnimatedCard>
          </MotiView>
        </View>

        {/* Quick Stats */}
        <View className="px-6 mb-6">
          <Text className="text-text-100 text-lg font-semibold mb-4">
            Your Progress
          </Text>

          <GridLayout columns={2} spacing={12} animated={true}>
            <StatsCard
              title="Accuracy"
              value={`${user.accuracyPercentage || 0}%`}
              subtitle={`${user.stats.correctAnswers}/${user.stats.totalQuestionsAnswered} correct`}
              icon={<Target size={20} color="#00FFFF" />}
              trend={
                user?.accuracyPercentage >= 70
                  ? "up"
                  : user?.accuracyPercentage >= 50
                  ? "neutral"
                  : "down"
              }
              trendValue={user?.accuracyPercentage >= 70 ? "+5%" : ""}
            />

            <StatsCard
              title="Quizzes"
              value={user.stats.totalQuizzesTaken.toString()}
              subtitle="Completed"
              icon={<BookOpen size={20} color="#FF6B6B" />}
            />

            <StatsCard
              title="Tests"
              value={user.stats.totalTestsTaken.toString()}
              subtitle="Completed"
              icon={<FileText size={20} color="#e0e0e0" />}
            />

            <StatsCard
              title="Avg Score"
              value={`${user.stats.averageScore}%`}
              subtitle="Overall performance"
              icon={<BarChart3 size={20} color="#00FFFF" />}
              trend={user.stats.averageScore >= 70 ? "up" : "neutral"}
            />
          </GridLayout>
        </View>

        {/* Study Progress */}
        <View className="px-6 mb-6">
          <Text className="text-text-100 text-lg font-semibold mb-4">
            Study Progress
          </Text>

          <View className="gap-4">
            <ProgressCard
              title="Questions Answered"
              progress={user.stats.totalQuestionsAnswered}
              total={1000}
              description="Keep going to unlock more content!"
              color="#FF6B6B"
            />

            <ProgressCard
              title="Weekly Goal"
              progress={Math.min(user.stats.currentStreak, 7)}
              total={7}
              description="Study every day this week"
              color="#00FFFF"
            />
          </View>
        </View>

        {/* Quick Actions */}
        <View className="px-6 mb-6">
          <Text className="text-text-100 text-lg font-semibold mb-4">
            Quick Actions
          </Text>

          <View className="gap-3">
            <AnimatedCard
              title="Today's Question"
              description="Start with your daily challenge"
              icon={<Calendar size={24} color="#00FFFF" />}
              onPress={() => router.push("/(tabs)/daily-question")}
              variant="accent"
            >
              <View className="flex-row items-center justify-between mt-3">
                <Text className="text-text-200 text-sm">
                  New question available
                </Text>
                <ChevronRight size={16} color="#00FFFF" />
              </View>
            </AnimatedCard>

            <AnimatedCard
              title="Practice Quiz"
              description="Test your knowledge on specific topics"
              icon={<BookOpen size={24} color="#FF6B6B" />}
              onPress={() => router.push("/(tabs)/quiz")}
              variant="primary"
            >
              <View className="flex-row items-center justify-between mt-3">
                <Text className="text-text-200 text-sm">
                  Choose your subject
                </Text>
                <ChevronRight size={16} color="#FF6B6B" />
              </View>
            </AnimatedCard>

            <AnimatedCard
              title="Full Test"
              description="Take a comprehensive exam"
              icon={<FileText size={24} color="#e0e0e0" />}
              onPress={() => router.push("/(tabs)/test")}
              variant="default"
            >
              <View className="flex-row items-center justify-between mt-3">
                <Text className="text-text-200 text-sm">
                  Simulate real exam conditions
                </Text>
                <ChevronRight size={16} color="#e0e0e0" />
              </View>
            </AnimatedCard>
          </View>
        </View>
      </ScrollView>

      {/* Floating Action Button */}
      <ExtendedFloatingActionButton
        onPress={() => router.push("/(tabs)/daily-question")}
        icon={<Play size={20} color="#FFFFFF" />}
        label="Study Now"
        variant="primary"
        position="bottom-right"
      />
    </View>
  );
}
