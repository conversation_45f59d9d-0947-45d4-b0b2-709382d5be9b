{"version": 3, "names": ["_codeFrame", "require", "_index", "_index2", "_cache", "_modification", "_parser", "_t", "_context", "FUNCTION_TYPES", "arrowFunctionExpression", "assignmentExpression", "awaitExpression", "blockStatement", "buildUndefinedNode", "callExpression", "cloneNode", "conditionalExpression", "expressionStatement", "getBindingIdentifiers", "identifier", "inheritLeadingComments", "inheritTrailingComments", "inheritsComments", "isBlockStatement", "isEmptyStatement", "isExpression", "isExpressionStatement", "isIfStatement", "isProgram", "isStatement", "isVariableDeclaration", "removeComments", "returnStatement", "sequenceExpression", "validate", "yieldExpression", "replaceWithMultiple", "nodes", "_getCached<PERSON><PERSON>s", "resync", "call", "_verifyNodeList", "node", "length", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "delete", "container", "key", "paths", "insertAfter", "requeue", "remove", "replaceWithSourceString", "replacement", "ast", "parse", "err", "loc", "message", "codeFrameColumns", "start", "line", "column", "code", "expressionAST", "program", "body", "expression", "traverse", "removeProperties", "replaceWith", "<PERSON><PERSON><PERSON>", "removed", "Error", "NodePath", "Array", "isArray", "nodePath", "isNodeType", "canHaveVariableDeclarationOrExpression", "canSwapBetweenExpressionAndStatement", "parentPath", "isExportDefaultDeclaration", "replaceExpressionWithStatements", "oldNode", "_replaceWith", "type", "setScope", "get", "_getCachedPaths2", "ReferenceError", "inList", "parent", "debug", "set", "declars", "nodesAsSingleExpression", "gatherSequenceExpressions", "id", "scope", "push", "functionParent", "getFunctionParent", "isParentAsync", "async", "isParentGenerator", "generator", "callee", "hoistVariables", "completionRecords", "getCompletionRecords", "path", "loop", "findParent", "isLoop", "uid", "getData", "generateDeclaredUidIdentifier", "pushContainer", "setData", "name", "arrowFunctionToExpression", "newCallee", "needToAwaitFunction", "hasType", "needToYieldFunction", "exprs", "ensureLastUndefined", "kind", "declar", "declarations", "bindings", "Object", "keys", "init", "consequent", "alternate", "test", "indexOf", "replaceInline", "_containerInsertAfter"], "sources": ["../../src/path/replacement.ts"], "sourcesContent": ["// This file contains methods responsible for replacing a node with another.\n\nimport { codeFrameColumns } from \"@babel/code-frame\";\nimport traverse from \"../index.ts\";\nimport NodePath from \"./index.ts\";\nimport { getCachedPaths } from \"../cache.ts\";\nimport { _verifyNodeList, _containerInsertAfter } from \"./modification.ts\";\nimport { parse } from \"@babel/parser\";\nimport {\n  FUNCTION_TYPES,\n  arrowFunctionExpression,\n  assignmentExpression,\n  awaitExpression,\n  blockStatement,\n  buildUndefinedNode,\n  callExpression,\n  cloneNode,\n  conditionalExpression,\n  expressionStatement,\n  getBindingIdentifiers,\n  identifier,\n  inheritLeadingComments,\n  inheritTrailingComments,\n  inheritsComments,\n  isBlockStatement,\n  isEmptyStatement,\n  isExpression,\n  isExpressionStatement,\n  isIfStatement,\n  isProgram,\n  isStatement,\n  isVariableDeclaration,\n  removeComments,\n  returnStatement,\n  sequenceExpression,\n  validate,\n  yieldExpression,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport { resync, setScope } from \"./context.ts\";\n\n/**\n * Replace a node with an array of multiple. This method performs the following steps:\n *\n *  - Inherit the comments of first provided node with that of the current node.\n *  - Insert the provided nodes after the current node.\n *  - Remove the current node.\n */\n\nexport function replaceWithMultiple(\n  this: NodePath,\n  nodes: t.Node | t.Node[],\n): NodePath[] {\n  resync.call(this);\n\n  nodes = _verifyNodeList.call(this, nodes);\n  inheritLeadingComments(nodes[0], this.node);\n  inheritTrailingComments(nodes[nodes.length - 1], this.node);\n  getCachedPaths(this)?.delete(this.node);\n  this.node =\n    // @ts-expect-error this.key must present in this.container\n    this.container[this.key] = null;\n  const paths = this.insertAfter(nodes);\n\n  if (this.node) {\n    this.requeue();\n  } else {\n    this.remove();\n  }\n  return paths;\n}\n\n/**\n * Parse a string as an expression and replace the current node with the result.\n *\n * NOTE: This is typically not a good idea to use. Building source strings when\n * transforming ASTs is an antipattern and SHOULD NOT be encouraged. Even if it's\n * easier to use, your transforms will be extremely brittle.\n */\n\nexport function replaceWithSourceString(this: NodePath, replacement: string) {\n  resync.call(this);\n  let ast: t.File;\n\n  try {\n    replacement = `(${replacement})`;\n    ast = parse(replacement);\n  } catch (err) {\n    const loc = err.loc;\n    if (loc) {\n      err.message +=\n        \" - make sure this is an expression.\\n\" +\n        codeFrameColumns(replacement, {\n          start: {\n            line: loc.line,\n            column: loc.column + 1,\n          },\n        });\n      err.code = \"BABEL_REPLACE_SOURCE_ERROR\";\n    }\n    throw err;\n  }\n\n  const expressionAST = (ast.program.body[0] as t.ExpressionStatement)\n    .expression;\n  traverse.removeProperties(expressionAST);\n  return this.replaceWith(expressionAST);\n}\n\n/**\n * Replace the current node with another.\n */\nexport function replaceWith<R extends t.Node>(\n  this: NodePath,\n  replacementPath: R,\n): [NodePath<R>];\nexport function replaceWith<R extends NodePath>(\n  this: NodePath,\n  replacementPath: R,\n): [R];\nexport function replaceWith(\n  this: NodePath,\n  replacementPath: t.Node | NodePath,\n): [NodePath] {\n  resync.call(this);\n\n  if (this.removed) {\n    throw new Error(\"You can't replace this node, we've already removed it\");\n  }\n\n  let replacement: t.Node =\n    replacementPath instanceof NodePath\n      ? replacementPath.node\n      : replacementPath;\n\n  if (!replacement) {\n    throw new Error(\n      \"You passed `path.replaceWith()` a falsy node, use `path.remove()` instead\",\n    );\n  }\n\n  if (this.node === replacement) {\n    return [this];\n  }\n\n  if (this.isProgram() && !isProgram(replacement)) {\n    throw new Error(\n      \"You can only replace a Program root node with another Program node\",\n    );\n  }\n\n  if (Array.isArray(replacement)) {\n    throw new Error(\n      \"Don't use `path.replaceWith()` with an array of nodes, use `path.replaceWithMultiple()`\",\n    );\n  }\n\n  if (typeof replacement === \"string\") {\n    throw new Error(\n      \"Don't use `path.replaceWith()` with a source string, use `path.replaceWithSourceString()`\",\n    );\n  }\n\n  let nodePath = \"\";\n\n  if (this.isNodeType(\"Statement\") && isExpression(replacement)) {\n    if (\n      !this.canHaveVariableDeclarationOrExpression() &&\n      !this.canSwapBetweenExpressionAndStatement(replacement) &&\n      !this.parentPath.isExportDefaultDeclaration()\n    ) {\n      // replacing a statement with an expression so wrap it in an expression statement\n      replacement = expressionStatement(replacement);\n      nodePath = \"expression\";\n    }\n  }\n\n  if (this.isNodeType(\"Expression\") && isStatement(replacement)) {\n    if (\n      !this.canHaveVariableDeclarationOrExpression() &&\n      !this.canSwapBetweenExpressionAndStatement(replacement)\n    ) {\n      // replacing an expression with a statement so let's explode it\n      return this.replaceExpressionWithStatements([replacement]) as [NodePath];\n    }\n  }\n\n  const oldNode = this.node;\n  if (oldNode) {\n    inheritsComments(replacement, oldNode);\n    removeComments(oldNode);\n  }\n\n  // replace the node\n  _replaceWith.call(this, replacement);\n  this.type = replacement.type;\n\n  // potentially create new scope\n  setScope.call(this);\n\n  // requeue for visiting\n  this.requeue();\n\n  return [nodePath ? this.get(nodePath) : this];\n}\n\nexport function _replaceWith(this: NodePath, node: t.Node) {\n  if (!this.container) {\n    throw new ReferenceError(\"Container is falsy\");\n  }\n\n  if (this.inList) {\n    // @ts-expect-error todo(flow->ts): check if validate accepts a numeric key\n    validate(this.parent, this.key, [node]);\n  } else {\n    validate(this.parent, this.key as string, node);\n  }\n\n  this.debug(`Replace with ${node?.type}`);\n  getCachedPaths(this)?.set(node, this).delete(this.node);\n\n  this.node =\n    // @ts-expect-error this.key must present in this.container\n    this.container[this.key] = node;\n}\n\n/**\n * This method takes an array of statements nodes and then explodes it\n * into expressions. This method retains completion records which is\n * extremely important to retain original semantics.\n */\n\nexport function replaceExpressionWithStatements(\n  this: NodePath,\n  nodes: Array<t.Statement>,\n) {\n  resync.call(this);\n\n  const declars: t.Identifier[] = [];\n  const nodesAsSingleExpression = gatherSequenceExpressions(nodes, declars);\n  if (nodesAsSingleExpression) {\n    for (const id of declars) this.scope.push({ id });\n    return this.replaceWith(nodesAsSingleExpression)[0].get(\"expressions\");\n  }\n\n  const functionParent = this.getFunctionParent();\n  const isParentAsync = functionParent?.node.async;\n  const isParentGenerator = functionParent?.node.generator;\n\n  const container = arrowFunctionExpression([], blockStatement(nodes));\n\n  this.replaceWith(callExpression(container, []));\n  // replaceWith changes the type of \"this\", but it isn't trackable by TS\n  type ThisType = NodePath<\n    t.CallExpression & {\n      callee: t.ArrowFunctionExpression & { body: t.BlockStatement };\n    }\n  >;\n\n  // hoist variable declaration in do block\n  // `(do { var x = 1; x;})` -> `var x; (() => { x = 1; return x; })()`\n  const callee = (this as ThisType).get(\"callee\");\n  callee.get(\"body\").scope.hoistVariables(id => this.scope.push({ id }));\n\n  // add implicit returns to all ending expression statements\n  const completionRecords: Array<NodePath> = callee.getCompletionRecords();\n  for (const path of completionRecords) {\n    if (!path.isExpressionStatement()) continue;\n\n    const loop = path.findParent(path => path.isLoop());\n    if (loop) {\n      let uid = loop.getData(\"expressionReplacementReturnUid\");\n\n      if (!uid) {\n        uid = callee.scope.generateDeclaredUidIdentifier(\"ret\");\n        callee\n          .get(\"body\")\n          .pushContainer(\"body\", returnStatement(cloneNode(uid)));\n        loop.setData(\"expressionReplacementReturnUid\", uid);\n      } else {\n        uid = identifier(uid.name);\n      }\n\n      path\n        .get(\"expression\")\n        .replaceWith(\n          assignmentExpression(\"=\", cloneNode(uid), path.node.expression),\n        );\n    } else {\n      path.replaceWith(returnStatement(path.node.expression));\n    }\n  }\n\n  // This is an IIFE, so we don't need to worry about the noNewArrows assumption\n  callee.arrowFunctionToExpression();\n  // Fixme: we can not `assert this is NodePath<t.FunctionExpression>` in `arrowFunctionToExpression`\n  // because it is not a class method known at compile time.\n  const newCallee = callee as unknown as NodePath<t.FunctionExpression>;\n\n  // (() => await xxx)() -> await (async () => await xxx)();\n  const needToAwaitFunction =\n    isParentAsync &&\n    traverse.hasType(\n      (this.get(\"callee.body\") as NodePath<t.BlockStatement>).node,\n      \"AwaitExpression\",\n      FUNCTION_TYPES,\n    );\n  const needToYieldFunction =\n    isParentGenerator &&\n    traverse.hasType(\n      (this.get(\"callee.body\") as NodePath<t.BlockStatement>).node,\n      \"YieldExpression\",\n      FUNCTION_TYPES,\n    );\n  if (needToAwaitFunction) {\n    newCallee.set(\"async\", true);\n    // yield* will await the generator return result\n    if (!needToYieldFunction) {\n      this.replaceWith(awaitExpression((this as ThisType).node));\n    }\n  }\n  if (needToYieldFunction) {\n    newCallee.set(\"generator\", true);\n    this.replaceWith(yieldExpression((this as ThisType).node, true));\n  }\n\n  return newCallee.get(\"body.body\");\n}\n\nfunction gatherSequenceExpressions(\n  nodes: ReadonlyArray<t.Node>,\n  declars: Array<t.Identifier>,\n) {\n  const exprs: t.Expression[] = [];\n  let ensureLastUndefined = true;\n\n  for (const node of nodes) {\n    // if we encounter emptyStatement before a non-emptyStatement\n    // we want to disregard that\n    if (!isEmptyStatement(node)) {\n      ensureLastUndefined = false;\n    }\n\n    if (isExpression(node)) {\n      exprs.push(node);\n    } else if (isExpressionStatement(node)) {\n      exprs.push(node.expression);\n    } else if (isVariableDeclaration(node)) {\n      if (node.kind !== \"var\") return; // bailed\n\n      for (const declar of node.declarations) {\n        const bindings = getBindingIdentifiers(declar);\n        for (const key of Object.keys(bindings)) {\n          declars.push(cloneNode(bindings[key]));\n        }\n\n        if (declar.init) {\n          exprs.push(assignmentExpression(\"=\", declar.id, declar.init));\n        }\n      }\n\n      ensureLastUndefined = true;\n    } else if (isIfStatement(node)) {\n      const consequent = node.consequent\n        ? gatherSequenceExpressions([node.consequent], declars)\n        : buildUndefinedNode();\n      const alternate = node.alternate\n        ? gatherSequenceExpressions([node.alternate], declars)\n        : buildUndefinedNode();\n      if (!consequent || !alternate) return; // bailed\n\n      exprs.push(conditionalExpression(node.test, consequent, alternate));\n    } else if (isBlockStatement(node)) {\n      const body = gatherSequenceExpressions(node.body, declars);\n      if (!body) return; // bailed\n\n      exprs.push(body);\n    } else if (isEmptyStatement(node)) {\n      // empty statement so ensure the last item is undefined if we're last\n      // checks if emptyStatement is first\n      if (nodes.indexOf(node) === 0) {\n        ensureLastUndefined = true;\n      }\n    } else {\n      // bailed, we can't turn this statement into an expression\n      return;\n    }\n  }\n\n  if (ensureLastUndefined) exprs.push(buildUndefinedNode());\n\n  if (exprs.length === 1) {\n    return exprs[0];\n  } else {\n    return sequenceExpression(exprs);\n  }\n}\n\nexport function replaceInline(this: NodePath, nodes: t.Node | Array<t.Node>) {\n  resync.call(this);\n\n  if (Array.isArray(nodes)) {\n    if (Array.isArray(this.container)) {\n      nodes = _verifyNodeList.call(this, nodes);\n      const paths = _containerInsertAfter.call(this, nodes);\n      this.remove();\n      return paths;\n    } else {\n      return this.replaceWithMultiple(nodes);\n    }\n  } else {\n    return this.replaceWith(nodes);\n  }\n}\n"], "mappings": ";;;;;;;;;;;AAEA,IAAAA,UAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,OAAA,GAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AACA,IAAAI,aAAA,GAAAJ,OAAA;AACA,IAAAK,OAAA,GAAAL,OAAA;AACA,IAAAM,EAAA,GAAAN,OAAA;AA+BA,IAAAO,QAAA,GAAAP,OAAA;AAAgD;EA9B9CQ,cAAc;EACdC,uBAAuB;EACvBC,oBAAoB;EACpBC,eAAe;EACfC,cAAc;EACdC,kBAAkB;EAClBC,cAAc;EACdC,SAAS;EACTC,qBAAqB;EACrBC,mBAAmB;EACnBC,qBAAqB;EACrBC,UAAU;EACVC,sBAAsB;EACtBC,uBAAuB;EACvBC,gBAAgB;EAChBC,gBAAgB;EAChBC,gBAAgB;EAChBC,YAAY;EACZC,qBAAqB;EACrBC,aAAa;EACbC,SAAS;EACTC,WAAW;EACXC,qBAAqB;EACrBC,cAAc;EACdC,eAAe;EACfC,kBAAkB;EAClBC,QAAQ;EACRC;AAAe,IAAA7B,EAAA;AAaV,SAAS8B,mBAAmBA,CAEjCC,KAAwB,EACZ;EAAA,IAAAC,eAAA;EACZC,eAAM,CAACC,IAAI,CAAC,IAAI,CAAC;EAEjBH,KAAK,GAAGI,6BAAe,CAACD,IAAI,CAAC,IAAI,EAAEH,KAAK,CAAC;EACzCjB,sBAAsB,CAACiB,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAACK,IAAI,CAAC;EAC3CrB,uBAAuB,CAACgB,KAAK,CAACA,KAAK,CAACM,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,CAACD,IAAI,CAAC;EAC3D,CAAAJ,eAAA,OAAAM,qBAAc,EAAC,IAAI,CAAC,aAApBN,eAAA,CAAsBO,MAAM,CAAC,IAAI,CAACH,IAAI,CAAC;EACvC,IAAI,CAACA,IAAI,GAEP,IAAI,CAACI,SAAS,CAAC,IAAI,CAACC,GAAG,CAAC,GAAG,IAAI;EACjC,MAAMC,KAAK,GAAG,IAAI,CAACC,WAAW,CAACZ,KAAK,CAAC;EAErC,IAAI,IAAI,CAACK,IAAI,EAAE;IACb,IAAI,CAACQ,OAAO,CAAC,CAAC;EAChB,CAAC,MAAM;IACL,IAAI,CAACC,MAAM,CAAC,CAAC;EACf;EACA,OAAOH,KAAK;AACd;AAUO,SAASI,uBAAuBA,CAAiBC,WAAmB,EAAE;EAC3Ed,eAAM,CAACC,IAAI,CAAC,IAAI,CAAC;EACjB,IAAIc,GAAW;EAEf,IAAI;IACFD,WAAW,GAAG,IAAIA,WAAW,GAAG;IAChCC,GAAG,GAAG,IAAAC,aAAK,EAACF,WAAW,CAAC;EAC1B,CAAC,CAAC,OAAOG,GAAG,EAAE;IACZ,MAAMC,GAAG,GAAGD,GAAG,CAACC,GAAG;IACnB,IAAIA,GAAG,EAAE;MACPD,GAAG,CAACE,OAAO,IACT,uCAAuC,GACvC,IAAAC,2BAAgB,EAACN,WAAW,EAAE;QAC5BO,KAAK,EAAE;UACLC,IAAI,EAAEJ,GAAG,CAACI,IAAI;UACdC,MAAM,EAAEL,GAAG,CAACK,MAAM,GAAG;QACvB;MACF,CAAC,CAAC;MACJN,GAAG,CAACO,IAAI,GAAG,4BAA4B;IACzC;IACA,MAAMP,GAAG;EACX;EAEA,MAAMQ,aAAa,GAAIV,GAAG,CAACW,OAAO,CAACC,IAAI,CAAC,CAAC,CAAC,CACvCC,UAAU;EACbC,cAAQ,CAACC,gBAAgB,CAACL,aAAa,CAAC;EACxC,OAAO,IAAI,CAACM,WAAW,CAACN,aAAa,CAAC;AACxC;AAaO,SAASM,WAAWA,CAEzBC,eAAkC,EACtB;EACZhC,eAAM,CAACC,IAAI,CAAC,IAAI,CAAC;EAEjB,IAAI,IAAI,CAACgC,OAAO,EAAE;IAChB,MAAM,IAAIC,KAAK,CAAC,uDAAuD,CAAC;EAC1E;EAEA,IAAIpB,WAAmB,GACrBkB,eAAe,YAAYG,eAAQ,GAC/BH,eAAe,CAAC7B,IAAI,GACpB6B,eAAe;EAErB,IAAI,CAAClB,WAAW,EAAE;IAChB,MAAM,IAAIoB,KAAK,CACb,2EACF,CAAC;EACH;EAEA,IAAI,IAAI,CAAC/B,IAAI,KAAKW,WAAW,EAAE;IAC7B,OAAO,CAAC,IAAI,CAAC;EACf;EAEA,IAAI,IAAI,CAACzB,SAAS,CAAC,CAAC,IAAI,CAACA,SAAS,CAACyB,WAAW,CAAC,EAAE;IAC/C,MAAM,IAAIoB,KAAK,CACb,oEACF,CAAC;EACH;EAEA,IAAIE,KAAK,CAACC,OAAO,CAACvB,WAAW,CAAC,EAAE;IAC9B,MAAM,IAAIoB,KAAK,CACb,yFACF,CAAC;EACH;EAEA,IAAI,OAAOpB,WAAW,KAAK,QAAQ,EAAE;IACnC,MAAM,IAAIoB,KAAK,CACb,2FACF,CAAC;EACH;EAEA,IAAII,QAAQ,GAAG,EAAE;EAEjB,IAAI,IAAI,CAACC,UAAU,CAAC,WAAW,CAAC,IAAIrD,YAAY,CAAC4B,WAAW,CAAC,EAAE;IAC7D,IACE,CAAC,IAAI,CAAC0B,sCAAsC,CAAC,CAAC,IAC9C,CAAC,IAAI,CAACC,oCAAoC,CAAC3B,WAAW,CAAC,IACvD,CAAC,IAAI,CAAC4B,UAAU,CAACC,0BAA0B,CAAC,CAAC,EAC7C;MAEA7B,WAAW,GAAGpC,mBAAmB,CAACoC,WAAW,CAAC;MAC9CwB,QAAQ,GAAG,YAAY;IACzB;EACF;EAEA,IAAI,IAAI,CAACC,UAAU,CAAC,YAAY,CAAC,IAAIjD,WAAW,CAACwB,WAAW,CAAC,EAAE;IAC7D,IACE,CAAC,IAAI,CAAC0B,sCAAsC,CAAC,CAAC,IAC9C,CAAC,IAAI,CAACC,oCAAoC,CAAC3B,WAAW,CAAC,EACvD;MAEA,OAAO,IAAI,CAAC8B,+BAA+B,CAAC,CAAC9B,WAAW,CAAC,CAAC;IAC5D;EACF;EAEA,MAAM+B,OAAO,GAAG,IAAI,CAAC1C,IAAI;EACzB,IAAI0C,OAAO,EAAE;IACX9D,gBAAgB,CAAC+B,WAAW,EAAE+B,OAAO,CAAC;IACtCrD,cAAc,CAACqD,OAAO,CAAC;EACzB;EAGAC,YAAY,CAAC7C,IAAI,CAAC,IAAI,EAAEa,WAAW,CAAC;EACpC,IAAI,CAACiC,IAAI,GAAGjC,WAAW,CAACiC,IAAI;EAG5BC,iBAAQ,CAAC/C,IAAI,CAAC,IAAI,CAAC;EAGnB,IAAI,CAACU,OAAO,CAAC,CAAC;EAEd,OAAO,CAAC2B,QAAQ,GAAG,IAAI,CAACW,GAAG,CAACX,QAAQ,CAAC,GAAG,IAAI,CAAC;AAC/C;AAEO,SAASQ,YAAYA,CAAiB3C,IAAY,EAAE;EAAA,IAAA+C,gBAAA;EACzD,IAAI,CAAC,IAAI,CAAC3C,SAAS,EAAE;IACnB,MAAM,IAAI4C,cAAc,CAAC,oBAAoB,CAAC;EAChD;EAEA,IAAI,IAAI,CAACC,MAAM,EAAE;IAEfzD,QAAQ,CAAC,IAAI,CAAC0D,MAAM,EAAE,IAAI,CAAC7C,GAAG,EAAE,CAACL,IAAI,CAAC,CAAC;EACzC,CAAC,MAAM;IACLR,QAAQ,CAAC,IAAI,CAAC0D,MAAM,EAAE,IAAI,CAAC7C,GAAG,EAAYL,IAAI,CAAC;EACjD;EAEA,IAAI,CAACmD,KAAK,CAAC,gBAAgBnD,IAAI,oBAAJA,IAAI,CAAE4C,IAAI,EAAE,CAAC;EACxC,CAAAG,gBAAA,OAAA7C,qBAAc,EAAC,IAAI,CAAC,aAApB6C,gBAAA,CAAsBK,GAAG,CAACpD,IAAI,EAAE,IAAI,CAAC,CAACG,MAAM,CAAC,IAAI,CAACH,IAAI,CAAC;EAEvD,IAAI,CAACA,IAAI,GAEP,IAAI,CAACI,SAAS,CAAC,IAAI,CAACC,GAAG,CAAC,GAAGL,IAAI;AACnC;AAQO,SAASyC,+BAA+BA,CAE7C9C,KAAyB,EACzB;EACAE,eAAM,CAACC,IAAI,CAAC,IAAI,CAAC;EAEjB,MAAMuD,OAAuB,GAAG,EAAE;EAClC,MAAMC,uBAAuB,GAAGC,yBAAyB,CAAC5D,KAAK,EAAE0D,OAAO,CAAC;EACzE,IAAIC,uBAAuB,EAAE;IAC3B,KAAK,MAAME,EAAE,IAAIH,OAAO,EAAE,IAAI,CAACI,KAAK,CAACC,IAAI,CAAC;MAAEF;IAAG,CAAC,CAAC;IACjD,OAAO,IAAI,CAAC5B,WAAW,CAAC0B,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAACR,GAAG,CAAC,aAAa,CAAC;EACxE;EAEA,MAAMa,cAAc,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;EAC/C,MAAMC,aAAa,GAAGF,cAAc,oBAAdA,cAAc,CAAE3D,IAAI,CAAC8D,KAAK;EAChD,MAAMC,iBAAiB,GAAGJ,cAAc,oBAAdA,cAAc,CAAE3D,IAAI,CAACgE,SAAS;EAExD,MAAM5D,SAAS,GAAGrC,uBAAuB,CAAC,EAAE,EAAEG,cAAc,CAACyB,KAAK,CAAC,CAAC;EAEpE,IAAI,CAACiC,WAAW,CAACxD,cAAc,CAACgC,SAAS,EAAE,EAAE,CAAC,CAAC;EAU/C,MAAM6D,MAAM,GAAI,IAAI,CAAcnB,GAAG,CAAC,QAAQ,CAAC;EAC/CmB,MAAM,CAACnB,GAAG,CAAC,MAAM,CAAC,CAACW,KAAK,CAACS,cAAc,CAACV,EAAE,IAAI,IAAI,CAACC,KAAK,CAACC,IAAI,CAAC;IAAEF;EAAG,CAAC,CAAC,CAAC;EAGtE,MAAMW,iBAAkC,GAAGF,MAAM,CAACG,oBAAoB,CAAC,CAAC;EACxE,KAAK,MAAMC,IAAI,IAAIF,iBAAiB,EAAE;IACpC,IAAI,CAACE,IAAI,CAACrF,qBAAqB,CAAC,CAAC,EAAE;IAEnC,MAAMsF,IAAI,GAAGD,IAAI,CAACE,UAAU,CAACF,IAAI,IAAIA,IAAI,CAACG,MAAM,CAAC,CAAC,CAAC;IACnD,IAAIF,IAAI,EAAE;MACR,IAAIG,GAAG,GAAGH,IAAI,CAACI,OAAO,CAAC,gCAAgC,CAAC;MAExD,IAAI,CAACD,GAAG,EAAE;QACRA,GAAG,GAAGR,MAAM,CAACR,KAAK,CAACkB,6BAA6B,CAAC,KAAK,CAAC;QACvDV,MAAM,CACHnB,GAAG,CAAC,MAAM,CAAC,CACX8B,aAAa,CAAC,MAAM,EAAEtF,eAAe,CAACjB,SAAS,CAACoG,GAAG,CAAC,CAAC,CAAC;QACzDH,IAAI,CAACO,OAAO,CAAC,gCAAgC,EAAEJ,GAAG,CAAC;MACrD,CAAC,MAAM;QACLA,GAAG,GAAGhG,UAAU,CAACgG,GAAG,CAACK,IAAI,CAAC;MAC5B;MAEAT,IAAI,CACDvB,GAAG,CAAC,YAAY,CAAC,CACjBlB,WAAW,CACV5D,oBAAoB,CAAC,GAAG,EAAEK,SAAS,CAACoG,GAAG,CAAC,EAAEJ,IAAI,CAACrE,IAAI,CAACyB,UAAU,CAChE,CAAC;IACL,CAAC,MAAM;MACL4C,IAAI,CAACzC,WAAW,CAACtC,eAAe,CAAC+E,IAAI,CAACrE,IAAI,CAACyB,UAAU,CAAC,CAAC;IACzD;EACF;EAGAwC,MAAM,CAACc,yBAAyB,CAAC,CAAC;EAGlC,MAAMC,SAAS,GAAGf,MAAmD;EAGrE,MAAMgB,mBAAmB,GACvBpB,aAAa,IACbnC,cAAQ,CAACwD,OAAO,CACb,IAAI,CAACpC,GAAG,CAAC,aAAa,CAAC,CAAgC9C,IAAI,EAC5D,iBAAiB,EACjBlC,cACF,CAAC;EACH,MAAMqH,mBAAmB,GACvBpB,iBAAiB,IACjBrC,cAAQ,CAACwD,OAAO,CACb,IAAI,CAACpC,GAAG,CAAC,aAAa,CAAC,CAAgC9C,IAAI,EAC5D,iBAAiB,EACjBlC,cACF,CAAC;EACH,IAAImH,mBAAmB,EAAE;IACvBD,SAAS,CAAC5B,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC;IAE5B,IAAI,CAAC+B,mBAAmB,EAAE;MACxB,IAAI,CAACvD,WAAW,CAAC3D,eAAe,CAAE,IAAI,CAAc+B,IAAI,CAAC,CAAC;IAC5D;EACF;EACA,IAAImF,mBAAmB,EAAE;IACvBH,SAAS,CAAC5B,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC;IAChC,IAAI,CAACxB,WAAW,CAACnC,eAAe,CAAE,IAAI,CAAcO,IAAI,EAAE,IAAI,CAAC,CAAC;EAClE;EAEA,OAAOgF,SAAS,CAAClC,GAAG,CAAC,WAAW,CAAC;AACnC;AAEA,SAASS,yBAAyBA,CAChC5D,KAA4B,EAC5B0D,OAA4B,EAC5B;EACA,MAAM+B,KAAqB,GAAG,EAAE;EAChC,IAAIC,mBAAmB,GAAG,IAAI;EAE9B,KAAK,MAAMrF,IAAI,IAAIL,KAAK,EAAE;IAGxB,IAAI,CAACb,gBAAgB,CAACkB,IAAI,CAAC,EAAE;MAC3BqF,mBAAmB,GAAG,KAAK;IAC7B;IAEA,IAAItG,YAAY,CAACiB,IAAI,CAAC,EAAE;MACtBoF,KAAK,CAAC1B,IAAI,CAAC1D,IAAI,CAAC;IAClB,CAAC,MAAM,IAAIhB,qBAAqB,CAACgB,IAAI,CAAC,EAAE;MACtCoF,KAAK,CAAC1B,IAAI,CAAC1D,IAAI,CAACyB,UAAU,CAAC;IAC7B,CAAC,MAAM,IAAIrC,qBAAqB,CAACY,IAAI,CAAC,EAAE;MACtC,IAAIA,IAAI,CAACsF,IAAI,KAAK,KAAK,EAAE;MAEzB,KAAK,MAAMC,MAAM,IAAIvF,IAAI,CAACwF,YAAY,EAAE;QACtC,MAAMC,QAAQ,GAAGjH,qBAAqB,CAAC+G,MAAM,CAAC;QAC9C,KAAK,MAAMlF,GAAG,IAAIqF,MAAM,CAACC,IAAI,CAACF,QAAQ,CAAC,EAAE;UACvCpC,OAAO,CAACK,IAAI,CAACrF,SAAS,CAACoH,QAAQ,CAACpF,GAAG,CAAC,CAAC,CAAC;QACxC;QAEA,IAAIkF,MAAM,CAACK,IAAI,EAAE;UACfR,KAAK,CAAC1B,IAAI,CAAC1F,oBAAoB,CAAC,GAAG,EAAEuH,MAAM,CAAC/B,EAAE,EAAE+B,MAAM,CAACK,IAAI,CAAC,CAAC;QAC/D;MACF;MAEAP,mBAAmB,GAAG,IAAI;IAC5B,CAAC,MAAM,IAAIpG,aAAa,CAACe,IAAI,CAAC,EAAE;MAC9B,MAAM6F,UAAU,GAAG7F,IAAI,CAAC6F,UAAU,GAC9BtC,yBAAyB,CAAC,CAACvD,IAAI,CAAC6F,UAAU,CAAC,EAAExC,OAAO,CAAC,GACrDlF,kBAAkB,CAAC,CAAC;MACxB,MAAM2H,SAAS,GAAG9F,IAAI,CAAC8F,SAAS,GAC5BvC,yBAAyB,CAAC,CAACvD,IAAI,CAAC8F,SAAS,CAAC,EAAEzC,OAAO,CAAC,GACpDlF,kBAAkB,CAAC,CAAC;MACxB,IAAI,CAAC0H,UAAU,IAAI,CAACC,SAAS,EAAE;MAE/BV,KAAK,CAAC1B,IAAI,CAACpF,qBAAqB,CAAC0B,IAAI,CAAC+F,IAAI,EAAEF,UAAU,EAAEC,SAAS,CAAC,CAAC;IACrE,CAAC,MAAM,IAAIjH,gBAAgB,CAACmB,IAAI,CAAC,EAAE;MACjC,MAAMwB,IAAI,GAAG+B,yBAAyB,CAACvD,IAAI,CAACwB,IAAI,EAAE6B,OAAO,CAAC;MAC1D,IAAI,CAAC7B,IAAI,EAAE;MAEX4D,KAAK,CAAC1B,IAAI,CAAClC,IAAI,CAAC;IAClB,CAAC,MAAM,IAAI1C,gBAAgB,CAACkB,IAAI,CAAC,EAAE;MAGjC,IAAIL,KAAK,CAACqG,OAAO,CAAChG,IAAI,CAAC,KAAK,CAAC,EAAE;QAC7BqF,mBAAmB,GAAG,IAAI;MAC5B;IACF,CAAC,MAAM;MAEL;IACF;EACF;EAEA,IAAIA,mBAAmB,EAAED,KAAK,CAAC1B,IAAI,CAACvF,kBAAkB,CAAC,CAAC,CAAC;EAEzD,IAAIiH,KAAK,CAACnF,MAAM,KAAK,CAAC,EAAE;IACtB,OAAOmF,KAAK,CAAC,CAAC,CAAC;EACjB,CAAC,MAAM;IACL,OAAO7F,kBAAkB,CAAC6F,KAAK,CAAC;EAClC;AACF;AAEO,SAASa,aAAaA,CAAiBtG,KAA6B,EAAE;EAC3EE,eAAM,CAACC,IAAI,CAAC,IAAI,CAAC;EAEjB,IAAImC,KAAK,CAACC,OAAO,CAACvC,KAAK,CAAC,EAAE;IACxB,IAAIsC,KAAK,CAACC,OAAO,CAAC,IAAI,CAAC9B,SAAS,CAAC,EAAE;MACjCT,KAAK,GAAGI,6BAAe,CAACD,IAAI,CAAC,IAAI,EAAEH,KAAK,CAAC;MACzC,MAAMW,KAAK,GAAG4F,mCAAqB,CAACpG,IAAI,CAAC,IAAI,EAAEH,KAAK,CAAC;MACrD,IAAI,CAACc,MAAM,CAAC,CAAC;MACb,OAAOH,KAAK;IACd,CAAC,MAAM;MACL,OAAO,IAAI,CAACZ,mBAAmB,CAACC,KAAK,CAAC;IACxC;EACF,CAAC,MAAM;IACL,OAAO,IAAI,CAACiC,WAAW,CAACjC,KAAK,CAAC;EAChC;AACF", "ignoreList": []}