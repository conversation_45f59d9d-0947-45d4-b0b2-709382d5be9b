/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow strict-local
 * @format
 */

import useAnimatedProps from './useAnimatedProps';
import useMergeRefs from '../Utilities/useMergeRefs';
import StyleSheet from '../../../exports/StyleSheet';
import View from '../../../exports/View';
import * as React from 'react';
export type AnimatedComponentType<-Props: {
  +[string]: mixed,
  ...
}, +Instance = mixed> = React.AbstractComponent<$ObjMap<Props & $ReadOnly<{
  passthroughAnimatedPropExplicitValues?: React.ElementConfig<typeof View>
}>, () => any>, Instance>;

/**
 * Experimental implementation of `createAnimatedComponent` that is intended to
 * be compatible with concurrent rendering.
 */
declare export default function createAnimatedComponent<TProps: {...}, TInstance>(Component: React.AbstractComponent<TProps, TInstance>): React.AbstractComponent<TProps, TInstance>;