# PhysioPrep - Comprehensive Exam Preparation App

A comprehensive exam preparation mobile application built with Expo/React Native and Node.js/Express/MongoDB backend.

## 🚀 Features Implemented

### ✅ Backend Infrastructure
- **Complete API Server** with Express.js
- **MongoDB Database** with Mongoose ODM
- **JWT Authentication** with bcryptjs password hashing
- **Email Service** with <PERSON><PERSON><PERSON><PERSON>
- **Daily Question System** with automated cron jobs
- **File Upload Support** with Multer
- **Comprehensive Error Handling** and validation
- **API Response Standards** (success/error format)

### ✅ Database Models
- **User Model** - Authentication, premium status, stats, preferences
- **Subject Model** - Categories, stats, hierarchical organization
- **Question Model** - MCQ format, difficulty levels, free/premium tiers
- **Quiz Model** - Timed sessions, scoring, analytics
- **Test Model** - Comprehensive exams, performance tracking
- **DailyQuestion Model** - Automated daily question rotation

### ✅ API Endpoints
- **Authentication** - Register, login, password reset, email verification
- **Questions** - CRUD operations, filtering, submission, bulk operations
- **Daily Questions** - Today's question, submission, history, analytics
- **Users** - Profile management, statistics
- **Subjects** - Category management
- **Quizzes & Tests** - Session management (placeholder)

### ✅ Frontend Foundation
- **Expo/React Native** with React Native Reusables UI
- **Recoil State Management** with comprehensive atoms
- **Custom Design System** with specified color palette
- **API Client** with Axios and error handling
- **Authentication Hook** with AsyncStorage persistence
- **Navigation Structure** with Expo Router
- **Responsive UI Components** with NativeWind styling

### ✅ Security & Validation
- **Input Validation** with express-validator
- **Rate Limiting** by user/IP
- **Premium Access Control** middleware
- **Resource Ownership** verification
- **CORS Configuration** for frontend integration

## 🏗️ Project Structure

```
PhysioPrep/
├── backend/                    # Node.js/Express API Server
│   ├── models/                # MongoDB/Mongoose models
│   ├── controllers/           # Route controllers
│   ├── routes/               # API routes
│   ├── middleware/           # Authentication, validation, error handling
│   ├── validators/           # Input validation schemas
│   ├── utils/               # Utilities (email, daily questions)
│   ├── config/              # Database configuration
│   ├── server.js            # Main server file
│   └── package.json         # Backend dependencies
├── app/                      # Expo Router pages
├── components/              # Reusable UI components
├── lib/                     # Utilities and configurations
│   ├── store/              # Recoil state management
│   ├── hooks/              # Custom React hooks
│   └── api/                # API client and services
└── package.json            # Frontend dependencies
```

## 🛠️ Technology Stack

### Frontend
- **Expo/React Native** - Mobile app framework
- **React Native Reusables** - UI component library
- **Recoil** - State management
- **NativeWind** - Styling with Tailwind CSS
- **Expo Router** - File-based navigation
- **Axios** - HTTP client
- **AsyncStorage** - Local data persistence
- **Moti** - Animations
- **Date-fns** - Date utilities
- **Yup** - Form validation

### Backend
- **Node.js/Express** - Server framework
- **MongoDB/Mongoose** - Database and ODM
- **JWT** - Authentication tokens
- **bcryptjs** - Password hashing
- **Nodemailer** - Email service
- **Node-cron** - Scheduled tasks
- **Express-validator** - Input validation
- **Multer** - File uploads
- **CORS** - Cross-origin resource sharing

## 🎨 Design System

The app uses a custom dark theme with the following color palette:

```css
Primary Colors:
- primary-100: #FF6B6B (Main brand color)
- primary-200: #dd4d51 (Hover state)
- primary-300: #8f001a (Dark variant)

Accent Colors:
- accent-100: #00FFFF (Cyan accent)
- accent-200: #00999b (Dark cyan)

Text Colors:
- text-100: #FFFFFF (Primary text)
- text-200: #e0e0e0 (Secondary text)

Background Colors:
- bg-100: #0F0F0F (Main background)
- bg-200: #1f1f1f (Card background)
- bg-300: #353535 (Border/divider)
```

## 🚀 Getting Started

### Prerequisites
- Node.js (v16 or higher)
- MongoDB (local or cloud instance)
- Expo CLI
- React Native development environment

### Backend Setup
1. Navigate to backend directory:
   ```bash
   cd backend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Configure environment variables:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. Start the server:
   ```bash
   npm run dev
   ```

### Frontend Setup
1. Navigate to PhysioPrep directory:
   ```bash
   cd PhysioPrep
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the Expo development server:
   ```bash
   npm run dev
   ```

## 📱 Core Features

### 1. Daily Questions
- Automated daily question rotation
- Mix of free and premium content (70% free, 30% premium)
- User progress tracking
- Statistics and analytics

### 2. Quiz Mode
- Timed quiz sessions
- Subject and difficulty filtering
- Immediate feedback
- Performance analytics

### 3. Test Mode
- Comprehensive exam simulation
- Time limits and passing scores
- Detailed performance reports
- Certificate generation

### 4. User Management
- JWT-based authentication
- Email verification
- Password reset functionality
- Premium subscription management

### 5. Question Management
- MCQ format with 2-6 options
- Difficulty levels (easy, medium, hard)
- Free/Premium tier system
- Bulk operations for admins

## 🔐 API Authentication

The API uses JWT tokens for authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## 📊 API Response Format

All API responses follow a consistent format:

**Success Response:**
```json
{
  "success": true,
  "data": {
    // Response data
  }
}
```

**Error Response:**
```json
{
  "success": false,
  "errors": [
    {
      "msg": "Error message"
    }
  ]
}
```

## 🎯 Next Steps

### Immediate Implementation Priorities:
1. **Authentication Screens** - Complete login/register UI
2. **Daily Question Interface** - Interactive question display
3. **Quiz Creation & Management** - Full quiz functionality
4. **Test Mode Implementation** - Comprehensive test interface
5. **User Dashboard** - Statistics and progress tracking
6. **Payment Integration** - Premium subscription handling

### Advanced Features:
1. **Offline Support** - Local data caching
2. **Push Notifications** - Daily reminders and updates
3. **Social Features** - Leaderboards and sharing
4. **Advanced Analytics** - Performance insights
5. **Content Management** - Admin panel for question management

## 🤝 Contributing

This is a comprehensive exam preparation platform designed to help physiotherapy students succeed in their exams. The foundation is now complete with a robust backend API and modern React Native frontend ready for feature implementation.

## 📄 License

This project is licensed under the MIT License.
