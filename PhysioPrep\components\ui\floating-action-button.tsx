import * as React from 'react';
import { Pressable } from 'react-native';
import { MotiView } from 'moti';
import { Text } from './text';
import { cn } from '~/lib/utils';

interface FloatingActionButtonProps {
  onPress: () => void;
  icon?: React.ReactNode;
  label?: string;
  variant?: 'primary' | 'accent' | 'secondary';
  size?: 'sm' | 'md' | 'lg';
  position?: 'bottom-right' | 'bottom-left' | 'bottom-center';
  className?: string;
  disabled?: boolean;
}

const variantStyles = {
  primary: 'bg-primary-100 shadow-primary-100/30',
  accent: 'bg-accent-100 shadow-accent-100/30',
  secondary: 'bg-bg-300 shadow-bg-300/30',
};

const sizeStyles = {
  sm: 'w-12 h-12',
  md: 'w-14 h-14',
  lg: 'w-16 h-16',
};

const positionStyles = {
  'bottom-right': 'absolute bottom-6 right-6',
  'bottom-left': 'absolute bottom-6 left-6',
  'bottom-center': 'absolute bottom-6 self-center',
};

export function FloatingActionButton({
  onPress,
  icon,
  label,
  variant = 'primary',
  size = 'md',
  position = 'bottom-right',
  className,
  disabled = false,
}: FloatingActionButtonProps) {
  const [isPressed, setIsPressed] = React.useState(false);

  return (
    <MotiView
      from={{ scale: 0, opacity: 0 }}
      animate={{ 
        scale: isPressed ? 0.9 : 1, 
        opacity: disabled ? 0.6 : 1 
      }}
      transition={{
        type: 'spring',
        damping: 15,
        stiffness: 150,
      }}
      className={cn(positionStyles[position], 'z-50')}
    >
      <Pressable
        onPress={onPress}
        onPressIn={() => !disabled && setIsPressed(true)}
        onPressOut={() => setIsPressed(false)}
        disabled={disabled}
        className={cn(
          'rounded-full items-center justify-center shadow-lg',
          variantStyles[variant],
          sizeStyles[size],
          className
        )}
      >
        {icon && (
          <MotiView
            animate={{
              rotate: isPressed ? '15deg' : '0deg',
            }}
            transition={{
              type: 'timing',
              duration: 150,
            }}
          >
            {icon}
          </MotiView>
        )}
        {label && !icon && (
          <Text className={cn(
            'font-semibold text-sm',
            variant === 'accent' ? 'text-bg-100' : 'text-text-100'
          )}>
            {label}
          </Text>
        )}
      </Pressable>
    </MotiView>
  );
}

interface ExtendedFABProps {
  onPress: () => void;
  icon: React.ReactNode;
  label: string;
  variant?: 'primary' | 'accent' | 'secondary';
  position?: 'bottom-right' | 'bottom-left' | 'bottom-center';
  className?: string;
  disabled?: boolean;
}

export function ExtendedFloatingActionButton({
  onPress,
  icon,
  label,
  variant = 'primary',
  position = 'bottom-right',
  className,
  disabled = false,
}: ExtendedFABProps) {
  const [isPressed, setIsPressed] = React.useState(false);

  return (
    <MotiView
      from={{ scale: 0, opacity: 0, translateX: position.includes('right') ? 100 : -100 }}
      animate={{ 
        scale: isPressed ? 0.95 : 1, 
        opacity: disabled ? 0.6 : 1,
        translateX: 0
      }}
      transition={{
        type: 'spring',
        damping: 15,
        stiffness: 150,
      }}
      className={cn(positionStyles[position], 'z-50')}
    >
      <Pressable
        onPress={onPress}
        onPressIn={() => !disabled && setIsPressed(true)}
        onPressOut={() => setIsPressed(false)}
        disabled={disabled}
        className={cn(
          'rounded-full flex-row items-center justify-center shadow-lg px-4 py-3 min-w-[120px]',
          variantStyles[variant],
          className
        )}
      >
        <MotiView
          animate={{
            rotate: isPressed ? '15deg' : '0deg',
          }}
          transition={{
            type: 'timing',
            duration: 150,
          }}
          className="mr-2"
        >
          {icon}
        </MotiView>
        <Text className={cn(
          'font-semibold text-sm',
          variant === 'accent' ? 'text-bg-100' : 'text-text-100'
        )}>
          {label}
        </Text>
      </Pressable>
    </MotiView>
  );
}
