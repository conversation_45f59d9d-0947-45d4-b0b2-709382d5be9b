import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';

export default function TestLayout() {
  return (
    <>
      <StatusBar style="light" backgroundColor="#0F0F0F" />
      <Stack
        screenOptions={{
          headerShown: false,
          contentStyle: { backgroundColor: '#0F0F0F' },
          animation: 'slide_from_right',
        }}
      >
        <Stack.Screen 
          name="index" 
          options={{
            title: 'Test Selection',
          }}
        />
        <Stack.Screen 
          name="[testId]" 
          options={{
            title: 'Test Setup',
          }}
        />
        <Stack.Screen 
          name="active/[testId]" 
          options={{
            title: 'Active Test',
            gestureEnabled: false, // Prevent back gesture during test
          }}
        />
        <Stack.Screen 
          name="results/[testId]" 
          options={{
            title: 'Test Results',
          }}
        />
      </Stack>
    </>
  );
}
