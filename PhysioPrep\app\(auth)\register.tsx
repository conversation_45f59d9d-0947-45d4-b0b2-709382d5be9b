import * as React from 'react';
import { View, ScrollView, KeyboardAvoidingView, Platform } from 'react-native';
import { useRouter } from 'expo-router';
import { MotiView } from 'moti';
import { ArrowLeft, Eye, EyeOff, Mail, Lock, User } from 'lucide-react-native';
import { useAuth } from '~/lib/hooks/useAuth';
import { Text } from '~/components/ui/text';
import { Button } from '~/components/ui/button';
import { AnimatedCard } from '~/components/ui/animated-card';

export default function RegisterScreen() {
  const router = useRouter();
  const { register, authLoading } = useAuth();
  const [showPassword, setShowPassword] = React.useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = React.useState(false);
  const [formData, setFormData] = React.useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
  });
  const [errors, setErrors] = React.useState<string[]>([]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setErrors([]);
  };

  const validateForm = () => {
    const newErrors: string[] = [];

    if (!formData.name.trim()) {
      newErrors.push('Name is required');
    }

    if (!formData.email.trim()) {
      newErrors.push('Email is required');
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.push('Please enter a valid email');
    }

    if (!formData.password) {
      newErrors.push('Password is required');
    } else if (formData.password.length < 6) {
      newErrors.push('Password must be at least 6 characters');
    }

    if (!formData.confirmPassword) {
      newErrors.push('Please confirm your password');
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.push('Passwords do not match');
    }

    setErrors(newErrors);
    return newErrors.length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      const result = await register({
        name: formData.name,
        email: formData.email,
        password: formData.password,
        confirmPassword: formData.confirmPassword,
      });
      
      if (result?.success) {
        router.replace('/(tabs)/home');
      } else {
        setErrors([result?.error || 'Registration failed']);
      }
    } catch (error) {
      setErrors(['An unexpected error occurred']);
    }
  };

  return (
    <View className='flex-1 bg-bg-100'>
      <KeyboardAvoidingView 
        className='flex-1'
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView 
          className='flex-1'
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ flexGrow: 1 }}
        >
          {/* Header */}
          <MotiView
            from={{ opacity: 0, translateY: -30 }}
            animate={{ opacity: 1, translateY: 0 }}
            transition={{ type: 'spring', damping: 15 }}
            className='flex-row items-center justify-between px-6 pt-16 pb-8'
          >
            <Button
              onPress={() => router.back()}
              variant="ghost"
              size="sm"
              className='p-2'
            >
              <ArrowLeft size={24} color="#e0e0e0" />
            </Button>

            <Text className='text-2xl font-bold text-primary-100'>
              Join PhysioPrep
            </Text>

            <View className='w-10' />
          </MotiView>

          {/* Form Container */}
          <View className='flex-1 px-6'>
            <MotiView
              from={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ type: 'spring', damping: 15, delay: 200 }}
            >
              <AnimatedCard
                title=""
                className='mb-6'
                variant="default"
              >
                <View className='p-6'>
                  {/* Error Messages */}
                  {errors.length > 0 && (
                    <MotiView
                      from={{ opacity: 0, translateY: -10 }}
                      animate={{ opacity: 1, translateY: 0 }}
                      className='bg-red-500/20 border border-red-500/30 rounded-lg p-3 mb-4'
                    >
                      {errors.map((error, index) => (
                        <Text key={index} className='text-red-400 text-sm'>
                          • {error}
                        </Text>
                      ))}
                    </MotiView>
                  )}

                  {/* Form Fields */}
                  <View className='gap-4'>
                    <View>
                      <Text className='text-text-200 text-sm mb-2'>Full Name</Text>
                      <View className='flex-row items-center bg-bg-300 rounded-lg px-4 py-3'>
                        <User size={20} color="#e0e0e0" />
                        <Text
                          className='flex-1 ml-3 text-text-100'
                          // This would be a TextInput in a real implementation
                        >
                          {formData.name || 'Enter your full name'}
                        </Text>
                      </View>
                    </View>

                    <View>
                      <Text className='text-text-200 text-sm mb-2'>Email</Text>
                      <View className='flex-row items-center bg-bg-300 rounded-lg px-4 py-3'>
                        <Mail size={20} color="#e0e0e0" />
                        <Text
                          className='flex-1 ml-3 text-text-100'
                          // This would be a TextInput in a real implementation
                        >
                          {formData.email || 'Enter your email'}
                        </Text>
                      </View>
                    </View>

                    <View>
                      <Text className='text-text-200 text-sm mb-2'>Password</Text>
                      <View className='flex-row items-center bg-bg-300 rounded-lg px-4 py-3'>
                        <Lock size={20} color="#e0e0e0" />
                        <Text
                          className='flex-1 ml-3 text-text-100'
                          // This would be a TextInput in a real implementation
                        >
                          {formData.password ? '••••••••' : 'Enter your password'}
                        </Text>
                        <Button
                          onPress={() => setShowPassword(!showPassword)}
                          variant="ghost"
                          size="sm"
                          className='p-1'
                        >
                          {showPassword ?
                            <EyeOff size={20} color="#e0e0e0" /> :
                            <Eye size={20} color="#e0e0e0" />
                          }
                        </Button>
                      </View>
                    </View>

                    <View>
                      <Text className='text-text-200 text-sm mb-2'>Confirm Password</Text>
                      <View className='flex-row items-center bg-bg-300 rounded-lg px-4 py-3'>
                        <Lock size={20} color="#e0e0e0" />
                        <Text
                          className='flex-1 ml-3 text-text-100'
                          // This would be a TextInput in a real implementation
                        >
                          {formData.confirmPassword ? '••••••••' : 'Confirm your password'}
                        </Text>
                        <Button
                          onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                          variant="ghost"
                          size="sm"
                          className='p-1'
                        >
                          {showConfirmPassword ?
                            <EyeOff size={20} color="#e0e0e0" /> :
                            <Eye size={20} color="#e0e0e0" />
                          }
                        </Button>
                      </View>
                    </View>

                    {/* Submit Button */}
                    <Button
                      onPress={handleSubmit}
                      disabled={authLoading}
                      className='w-full bg-primary-100 py-4 mt-4'
                    >
                      <Text className='text-text-100 font-semibold text-lg'>
                        {authLoading ? 'Creating Account...' : 'Create Account'}
                      </Text>
                    </Button>
                  </View>
                </View>
              </AnimatedCard>
            </MotiView>

            {/* Mode Switching */}
            <MotiView
              from={{ opacity: 0, translateY: 20 }}
              animate={{ opacity: 1, translateY: 0 }}
              transition={{ type: 'spring', damping: 15, delay: 400 }}
              className='items-center gap-4'
            >
              <View className='flex-row items-center'>
                <Text className='text-text-200'>Already have an account? </Text>
                <Button
                  onPress={() => router.push('/(auth)/login')}
                  variant="ghost"
                  className='p-0'
                >
                  <Text className='text-primary-100 font-semibold'>Sign In</Text>
                </Button>
              </View>
            </MotiView>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
}
