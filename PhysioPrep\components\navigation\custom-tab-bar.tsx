import * as React from 'react';
import { View, Pressable, Platform } from 'react-native';
import { BottomTabBarProps } from '@react-navigation/bottom-tabs';
import { Mo<PERSON>View } from 'moti';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Text } from '~/components/ui/text';

interface CustomTabBarProps extends BottomTabBarProps {}

export function CustomTabBar({ state, descriptors, navigation }: CustomTabBarProps) {
  const insets = useSafeAreaInsets();

  return (
    <View 
      className='absolute bottom-0 left-0 right-0 bg-bg-200/95 backdrop-blur-lg border-t border-bg-300'
      style={{ 
        paddingBottom: insets.bottom + (Platform.OS === 'ios' ? 0 : 16),
        paddingTop: 12,
      }}
    >
      <View className='flex-row items-center justify-around px-4'>
        {state.routes.map((route, index) => {
          const { options } = descriptors[route.key];
          const label =
            options.tabBarLabel !== undefined
              ? options.tabBarLabel
              : options.title !== undefined
              ? options.title
              : route.name;

          const isFocused = state.index === index;

          const onPress = () => {
            const event = navigation.emit({
              type: 'tabPress',
              target: route.key,
              canPreventDefault: true,
            });

            if (!isFocused && !event.defaultPrevented) {
              navigation.navigate(route.name, route.params);
            }
          };

          const onLongPress = () => {
            navigation.emit({
              type: 'tabLongPress',
              target: route.key,
            });
          };

          // Get the icon component
          const IconComponent = options.tabBarIcon?.({
            focused: isFocused,
            color: isFocused ? '#FF6B6B' : '#e0e0e0',
            size: 24,
          });

          return (
            <Pressable
              key={route.key}
              accessibilityRole="button"
              accessibilityState={isFocused ? { selected: true } : {}}
              accessibilityLabel={options.tabBarAccessibilityLabel}
              testID={options.tabBarTestID}
              onPress={onPress}
              onLongPress={onLongPress}
              className='flex-1 items-center py-2'
            >
              <MotiView
                animate={{
                  scale: isFocused ? 1.1 : 1,
                  opacity: isFocused ? 1 : 0.7,
                }}
                transition={{
                  type: 'spring',
                  damping: 15,
                  stiffness: 150,
                }}
                className='items-center'
              >
                {/* Active indicator */}
                {isFocused && (
                  <MotiView
                    from={{ scale: 0, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ type: 'spring', damping: 15 }}
                    className='absolute -top-1 w-1 h-1 bg-primary-100 rounded-full'
                  />
                )}

                {/* Icon */}
                <View className='mb-1'>
                  {IconComponent}
                </View>

                {/* Label */}
                <Text 
                  className={`text-xs font-medium ${
                    isFocused ? 'text-primary-100' : 'text-text-200'
                  }`}
                >
                  {label as string}
                </Text>

                {/* Active background */}
                {isFocused && (
                  <MotiView
                    from={{ scale: 0, opacity: 0 }}
                    animate={{ scale: 1, opacity: 0.1 }}
                    transition={{ type: 'spring', damping: 15, delay: 100 }}
                    className='absolute inset-0 bg-primary-100 rounded-lg -m-2'
                  />
                )}
              </MotiView>
            </Pressable>
          );
        })}
      </View>
    </View>
  );
}
